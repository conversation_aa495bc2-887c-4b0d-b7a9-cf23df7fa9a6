# Budget Agreement Discount System - Complete End-to-End Guide

## Table of Contents
1. [Project Overview](#project-overview)
2. [Complete User Workflow](#complete-user-workflow)
3. [Architecture & Project Structure](#architecture--project-structure)
4. [Budget Management System](#budget-management-system)
5. [Agreement Management System](#agreement-management-system)
6. [Discount Management System](#discount-management-system)
7. [State Management & Data Flow](#state-management--data-flow)
8. [Navigation & Routing](#navigation--routing)
9. [Component Architecture](#component-architecture)
10. [Form Validation & Error Handling](#form-validation--error-handling)
11. [API Integration Patterns](#api-integration-patterns)
12. [Conditional Logic & Business Rules](#conditional-logic--business-rules)
13. [Development Patterns & Best Practices](#development-patterns--best-practices)
14. [Complete Usage Examples](#complete-usage-examples)

## Project Overview

The Budget Agreement Discount System is a comprehensive React-based enterprise application for managing telecommunications discount agreements within budget frameworks. This system provides a complete workflow from budget creation through agreement management to complex discount configurations with hierarchical structures.

### System Purpose
This application serves as a central platform for telecommunications companies to:
- **Create and manage budgets** for different time periods and operators
- **Define agreements** within budget frameworks with specific parameters
- **Configure complex discount models** with 14 different calculation types
- **Manage hierarchical discount structures** (parent discounts and sub-discounts)
- **Handle traffic segmentation** and operator-specific configurations
- **Support multi-currency operations** with various settlement methods
- **Apply qualifying rules** for discount eligibility and conditions

### Key Business Features
- **Budget Lifecycle Management**: Complete budget creation, editing, calculation, and reporting
- **Agreement Portfolio Management**: Multiple agreements per budget with activation/deactivation
- **Advanced Discount Modeling**: 14 sophisticated discount model types for various business scenarios
- **Hierarchical Discount Structure**: Parent discounts with nested sub-discounts
- **Dynamic Form Validation**: Context-aware validation based on model types and business rules
- **Conditional Field Rendering**: Smart form fields that appear/disappear based on selections
- **Complex Parameter Management**: Multiple calculation types with bounds and constraints
- **Traffic Segmentation**: Operator-specific and geography-based traffic configurations
- **Multi-currency Support**: Global currency support with settlement methods
- **Qualifying Rules System**: Sophisticated eligibility criteria for discount applications

### Technical Architecture Overview
- **Frontend**: React 18 with modern hooks and functional components
- **State Management**: Redux with Redux Toolkit for predictable state updates
- **Form Management**: Formik for complex form handling with Yup validation
- **UI Framework**: Material-UI for consistent design system
- **API Communication**: Axios with interceptors for HTTP requests
- **Routing**: React Router for SPA navigation
- **Real-time Updates**: WebSocket integration for live data updates
- **Testing**: Jest and React Testing Library for comprehensive testing

### Main System Components
- **Budget Management System**: Top-level budget container with parameters and calculations
- **Agreement Management System**: Individual agreements within budgets with lifecycle management
- **Discount Management System**: Complex discount creation, editing, and deletion with validation
- **Sub-Discount Management System**: Nested discount structures with inheritance rules
- **Form Validation Engine**: Comprehensive validation system with conditional rules
- **API Integration Layer**: RESTful API communication with error handling
- **State Management Layer**: Redux-based state management with normalized data structures
- **Navigation System**: Route-based navigation with context preservation

## Complete User Workflow

### End-to-End User Journey

#### 1. Application Entry Point
```
User Access → Authentication → Budget List Page (/budgets)
```

#### 2. Budget Creation Workflow
```
Budget List → Create Budget Button → Budget Creation Modal → Form Completion → Budget Created → Navigate to Budget Details
```

#### 3. Agreement Management Workflow
```
Budget Details → Agreements Tab → Create Agreement → Agreement Form → Agreement Created → Navigate to Agreement Details
```

#### 4. Discount Management Workflow
```
Agreement Details → Discounts Tab → Create Discount → Discount Form → Model Type Selection → Field Configuration → Discount Created
```

#### 5. Sub-Discount Management Workflow
```
Discount Created → Sub-Discount Creation → Limited Model Types → Additional Attributes → Sub-Discount Created
```

### Complete Navigation Flow

#### Primary Navigation Paths
1. **Budget List** (`/budgets`) - Entry point showing all budgets
2. **Budget Details** (`/budgets/:id`) - Individual budget management
3. **Agreement Details** (`/budgets/:budgetId/agreements/:agreementId`) - Individual agreement management

#### Modal-Based Operations
- **Budget Creation**: Modal overlay on Budget List page
- **Agreement Creation**: Modal overlay on Budget Details page
- **Discount Creation/Editing**: Modal overlay on Agreement Details page
- **Sub-Discount Management**: Nested modals within discount operations

#### Context Preservation
- **Budget Context**: Maintained throughout budget operations
- **Agreement Context**: Shared between agreement and discount operations
- **Form State**: Preserved during modal operations and navigation

### User Interaction Patterns

#### 1. List → Details → Management Pattern
```
List View (Table/Cards) → Click Item → Details Page → Management Actions (Create/Edit/Delete)
```

#### 2. Modal-Based CRUD Operations
```
Trigger Action → Modal Opens → Form Interaction → Submit/Cancel → Modal Closes → List Refreshes
```

#### 3. Hierarchical Data Management
```
Parent Entity → Child Entity List → Child Entity Management → Parent Entity Update
```

#### 4. Context-Aware Navigation
```
Current Context → Action Trigger → Context Preservation → Operation Completion → Context Restoration
```

## Architecture & Project Structure

### Complete Directory Structure
```
src/
├── App.jsx                          # Main application component
├── AppContextProvider.jsx           # Global application context
├── AppProvider.jsx                  # Application providers wrapper
├── BreadcrumbsConfig.jsx            # Breadcrumb navigation configuration
├── core/                            # Core application utilities
│   ├── configs/                     # Configuration files
│   │   └── paths.js                 # Route path definitions
│   ├── hooks/                       # Custom React hooks
│   ├── interceptors/                # HTTP interceptors
│   ├── rootReducer.js              # Redux root reducer
│   ├── services/                    # Core services
│   │   ├── HTTPService.js          # HTTP client configuration
│   │   ├── CookiesService.js       # Cookie management
│   │   └── SocketService.js        # WebSocket service
│   ├── state/                       # Global state management
│   │   └── AppVariables/           # Application variables state
│   ├── store.js                    # Redux store configuration
│   └── utilities/                   # Utility functions
├── features/                        # Reusable feature components
│   ├── BudgetCalculation/          # Budget calculation features
│   ├── BudgetDeleting/             # Budget deletion features
│   ├── GetAgreements/              # Agreement fetching
│   ├── GetTrafficSegments/         # Traffic segment management
│   ├── HomeOperatorsAutocomplete/  # Operator selection components
│   ├── CurrenciesAutocomplete/     # Currency selection components
│   └── [other features]/          # Additional feature modules
├── layouts/                         # Application layouts
│   └── BaseLayout/                 # Main application layout
│       ├── BaseLayout.jsx          # Layout component
│       └── routesConfig.js         # Route configuration
├── pages/                          # Main application pages
│   ├── BudgetList/                 # Budget list and creation
│   │   ├── BudgetCreation/         # Budget creation modal
│   │   │   └── BudgetCreationModalContent/
│   │   │       ├── BudgetCreationMainForm/    # Main budget form
│   │   │       ├── CreateBudget/              # Budget creation logic
│   │   │       └── CreateDefaultForecastRules/ # Default rules creation
│   │   ├── BudgetList.jsx          # Budget list component
│   │   ├── BudgetListContainer.jsx # Budget list container
│   │   ├── GetBudgets/             # Budget fetching logic
│   │   └── [other budget components]/
│   ├── BudgetDetails/              # Budget details and management
│   │   ├── BudgetDetailsContextProvider.jsx  # Budget context
│   │   ├── BudgetItems/            # Budget items management
│   │   │   ├── Agreements/         # Agreement management
│   │   │   │   ├── CreateAgreement/        # Agreement creation
│   │   │   │   ├── AgreementsTable/        # Agreement table display
│   │   │   │   ├── ActivateAllAgreements/  # Bulk operations
│   │   │   │   └── [other agreement features]/
│   │   │   └── ForecastRules/      # Forecast rules management
│   │   ├── BudgetParameters/       # Budget parameter management
│   │   ├── BudgetValues/           # Budget value calculations
│   │   └── [other budget features]/
│   └── AgreementDetails/           # Agreement details and management
│       ├── AgreementDetailsContextProvider.jsx # Agreement context
│       ├── AgreementItems/         # Agreement items management
│       │   ├── Discounts/          # Discount management system
│       │   │   ├── shared/         # Shared discount components
│       │   │   │   ├── DiscountForm/           # Main discount form
│       │   │   │   │   ├── DiscountTraffic/    # Traffic configuration
│       │   │   │   │   ├── DiscountParameters/ # Parameter configuration
│       │   │   │   │   ├── DiscountQualifying/ # Qualifying rules
│       │   │   │   │   ├── DiscountModelType/  # Model type selection
│       │   │   │   │   └── AdditionalAttributes/ # Additional fields
│       │   │   │   └── DiscountChilds/         # Sub-discount management
│       │   │   ├── DiscountCreation/           # Discount creation modal
│       │   │   ├── DiscountEditing/            # Discount editing modal
│       │   │   ├── DiscountDeleting/           # Discount deletion
│       │   │   ├── SubDiscountCreation/        # Sub-discount creation
│       │   │   ├── SubDiscountEditing/         # Sub-discount editing
│       │   │   ├── GetDiscounts/               # Discount fetching
│       │   │   └── DiscountsTable/             # Discount table display
│       │   └── ForecastRules/      # Agreement forecast rules
│       ├── AgreementParameters/    # Agreement parameter management
│       └── AgreementValues/        # Agreement value calculations
└── shared/                         # Shared UI components
    ├── Autocomplete/               # Generic autocomplete component
    ├── FormattedNumber/            # Number formatting component
    ├── RangePicker/                # Date range picker
    ├── RadioButtons/               # Radio button groups
    ├── InfoIconWithTooltip/        # Tooltip components
    └── [other shared components]/  # Additional shared components
```

### Core Technologies & Dependencies
- **React 18.2+** - Modern React with concurrent features and hooks
- **Redux Toolkit** - Predictable state management with modern Redux patterns
- **React Router 6** - Declarative routing for single-page applications
- **Formik 2.2+** - Build forms without tears with validation support
- **Yup** - JavaScript schema builder for value parsing and validation
- **Material-UI (MUI) 5** - React components implementing Google's Material Design
- **Axios** - Promise-based HTTP client for API communication
- **Day.js** - Lightweight date manipulation library
- **React Perfect Scrollbar** - Custom scrollbar component
- **React Cookie** - Cookie management for React applications
- **Socket.IO Client** - Real-time bidirectional event-based communication

### Data Flow Architecture
```
User Interaction → Component → Action Creator → API Service → Redux Store → Component Re-render
                                     ↓
                              Context Provider → Child Components
```

#### Hierarchical Data Structure
1. **Budget** (Top Level)
   - Contains multiple **Agreements**
   - Has budget parameters, calculations, and reporting
   - Manages global budget filters and settings

2. **Agreement** (Second Level)
   - Belongs to a specific **Budget**
   - Contains multiple **Discounts**
   - Has agreement parameters and calculations
   - Manages agreement-specific filters and settings

3. **Discount** (Third Level)
   - Belongs to a specific **Agreement**
   - Can contain multiple **Sub-Discounts**
   - Has complex model types and parameters
   - Manages discount-specific configurations

4. **Sub-Discount** (Fourth Level)
   - Belongs to a specific **Discount**
   - Limited model types compared to parent discounts
   - Inherits some properties from parent discount
   - Has additional attributes specific to sub-discounts

## Budget Management System

### Budget Creation Workflow

#### Step 1: Budget List Page Entry
- **Route**: `/budgets`
- **Component**: `BudgetListContainer`
- **Purpose**: Display all existing budgets with creation capability
- **Features**:
  - Budget list with filtering and sorting
  - Budget creation button
  - Budget comparison features
  - KPI totals and statistics

#### Step 2: Budget Creation Modal
- **Trigger**: "Create Budget" button click
- **Component**: `BudgetCreationModalContent`
- **Modal Structure**:
  ```
  BudgetCreationModal
  ├── BudgetCreationModalContent
  │   ├── BudgetCreationMainForm (Core Fields)
  │   ├── BudgetCreationMethod (Creation Method Selection)
  │   ├── DefaultForecastRulesCreationForm (Optional Rules)
  │   └── BudgetCreationModalActions (Submit/Cancel)
  ```

#### Step 3: Budget Form Fields

##### Required Fields
1. **Budget Name**
   - **Field**: `name`
   - **Type**: Text input
   - **Validation**: Required, non-empty string
   - **Purpose**: Unique identifier for the budget

2. **Home Operators**
   - **Field**: `homeOperators`
   - **Type**: Multi-select autocomplete
   - **Validation**: Minimum 1 operator required
   - **Purpose**: Define which operators this budget applies to

3. **Budget Type**
   - **Field**: `type`
   - **Type**: Single-select autocomplete
   - **Validation**: Required selection
   - **Purpose**: Categorize budget for reporting and filtering

4. **Validity Period**
   - **Fields**: `startDate`, `endDate`
   - **Type**: Date range picker
   - **Validation**: Valid date range, start < end
   - **Purpose**: Define budget operational period

5. **Last Historical Month**
   - **Field**: `lastHistoricalMonth`
   - **Type**: Month picker
   - **Validation**: Must be within budget period
   - **Purpose**: Define historical data cutoff for calculations

##### Optional Fields
1. **Description**
   - **Field**: `description`
   - **Type**: Multi-line text input
   - **Validation**: Maximum 255 characters
   - **Purpose**: Additional budget information

#### Step 4: Budget Creation Methods

##### Method 1: Fill with Historical Traffic
- **Value**: `FILL_WITH_HISTORICAL_TRAFFIC`
- **Purpose**: Create budget with historical data population
- **Process**:
  1. Create budget with `run_calculation: true`
  2. Automatically populate forecast rules based on historical data
  3. Trigger budget calculation after creation

##### Method 2: Default Forecast Method
- **Value**: `DEFAULT_FORECAST_METHOD`
- **Purpose**: Create budget with predefined forecast rules
- **Process**:
  1. Create budget with `run_calculation: false`
  2. Create default forecast rules based on configuration
  3. Manual calculation trigger required

##### Method 3: Empty Budget
- **Value**: `EMPTY_BUDGET`
- **Purpose**: Create empty budget for manual configuration
- **Process**:
  1. Create budget with minimal configuration
  2. No automatic rules or calculations
  3. Full manual setup required

#### Step 5: Budget Creation API Flow

##### API Request Structure
```javascript
const budgetData = {
  start_date: "2025-01-01",
  end_date: "2025-12-31",
  home_operators: [36488, 36489], // Operator IDs
  run_calculation: true/false,     // Based on creation method
  name: "Q1 2025 Budget",
  description: "First quarter budget for 2025",
  type: "QUARTERLY",               // Budget type value
  last_historical_month: "2024-12-01"
};
```

##### API Endpoint
- **Method**: `POST`
- **URL**: `/budgets`
- **Service**: `createBudget(budgetData)`
- **Action**: `createBudgetAction(budgetData)`

##### Success Flow
1. Budget created successfully
2. Navigate to budget details: `/budgets/{budgetId}`
3. Display success notification
4. If default forecast method: Create default forecast rules
5. If historical traffic method: Trigger budget calculation

##### Error Handling
1. Display error notification with API message
2. Keep modal open for correction
3. Highlight validation errors in form
4. Preserve user input for retry

#### Step 6: Post-Creation Navigation
- **Success Route**: `/budgets/{budgetId}`
- **Component**: `BudgetDetails`
- **Context**: `BudgetDetailsContextProvider`
- **Available Actions**:
  - View budget parameters
  - Manage agreements
  - Configure forecast rules
  - Run budget calculations
  - View budget reports

### Budget Details Page Structure

#### Main Components
1. **Budget Parameters Section**
   - Display budget configuration
   - Edit budget parameters
   - Budget status and calculations

2. **Budget Items Section**
   - **Agreements Tab**: Manage agreements within budget
   - **Forecast Rules Tab**: Configure forecast rules

3. **Budget Values Section**
   - KPI values and calculations
   - Traffic evolution charts
   - Budget reporting dashboard

#### Budget Context Provider
```javascript
const BudgetDetailsContext = {
  budgetId,                    // Current budget ID
  globalBudgetFilters,         // Shared filters across budget
  triggerBudgetCalculationModal, // Calculation trigger
  countryView,                 // Country-specific view settings
  isGlobalDateFilterModified,  // Filter modification tracking
  // ... other budget-specific state
};
```

## Agreement Management System

### Agreement Creation Workflow

#### Step 1: Navigate to Budget Details
- **Route**: `/budgets/{budgetId}`
- **Component**: `BudgetDetails`
- **Tab**: Agreements
- **Component**: `Agreements`

#### Step 2: Agreement Creation Trigger
- **Button**: "Create Agreement"
- **Modal**: `CreateAgreementModal`
- **Component**: `CreateAgreementModalContent`

#### Step 3: Agreement Form Fields

##### Required Fields
1. **Agreement Name**
   - **Field**: `name`
   - **Type**: Text input
   - **Validation**: Required, unique within budget

2. **Partner Operators**
   - **Field**: `partnerOperators`
   - **Type**: Multi-select autocomplete
   - **Validation**: Minimum 1 operator required

3. **Validity Period**
   - **Fields**: `startDate`, `endDate`
   - **Type**: Date range picker
   - **Validation**: Must be within budget period

4. **Agreement Type**
   - **Field**: `agreementType`
   - **Type**: Single-select dropdown
   - **Options**: Bilateral, Multilateral, etc.

5. **Negotiator**
   - **Field**: `negotiator`
   - **Type**: Single-select autocomplete
   - **Purpose**: Assign responsible person

##### Optional Fields
1. **Description**
   - **Field**: `description`
   - **Type**: Multi-line text input

2. **Reference Number**
   - **Field**: `referenceNumber`
   - **Type**: Text input

#### Step 4: Agreement Creation API Flow

##### API Request Structure
```javascript
const agreementData = {
  budget_id: budgetId,
  name: "Agreement with Operator XYZ",
  partner_operators: [36490, 36491],
  start_date: "2025-01-01",
  end_date: "2025-12-31",
  agreement_type: "BILATERAL",
  negotiator_id: 123,
  description: "Bilateral agreement for voice and SMS traffic",
  reference_number: "AGR-2025-001"
};
```

##### API Endpoint
- **Method**: `POST`
- **URL**: `/budgets/{budgetId}/agreements`
- **Service**: `createAgreement(budgetId, agreementData)`
- **Action**: `createAgreementAction(budgetId, agreementData)`

#### Step 5: Post-Creation Navigation
- **Success Route**: `/budgets/{budgetId}/agreements/{agreementId}`
- **Component**: `AgreementDetails`
- **Context**: `AgreementDetailsContextProvider`

### Agreement Details Page Structure

#### Main Components
1. **Agreement Parameters Section**
   - Display agreement configuration
   - Edit agreement parameters
   - Agreement status and activation

2. **Agreement Items Section**
   - **Discounts Tab**: Manage discounts within agreement
   - **Forecast Rules Tab**: Agreement-specific forecast rules

3. **Agreement Values Section**
   - Agreement KPI values
   - Traffic calculations
   - Agreement reporting

#### Agreement Context Provider
```javascript
const AgreementDetailsContext = {
  agreementId,              // Current agreement ID
  budgetId,                 // Parent budget ID
  agreementParameters,      // Agreement configuration
  agreementParametersData,  // Full agreement data
  globalAgreementFilters,   // Agreement-specific filters
  // ... other agreement-specific state
};
```

## Discount Management System

### Discount Model Types Overview

The system supports 14 distinct discount model types, each with unique configurations and business logic:

#### 1. Single Rate Effective (SRE)
- **Value**: `SINGLE_RATE_EFFECTIVE`
- **Business Purpose**: Simple single-rate discount model for straightforward pricing
- **Settlement Method**: Credit Note EoA (End of Agreement)
- **Parameters**: Single rate with volume bounds
- **Calculation**: Fixed rate applied to traffic volume
- **Use Case**: Basic discount scenarios, simple bilateral agreements
- **Configuration**:
  - Single parameter row
  - Volume-based bounds
  - Value-based discount basis

#### 2. Stepped/Tiered
- **Value**: `STEPPED_TIERED`
- **Business Purpose**: Multi-tier discount structure for volume incentives
- **Parameters**: Multiple tiers with different rates and bounds
- **Calculation**: Different rates applied based on volume tiers
- **Use Case**: Volume-based progressive discounts, encouraging higher traffic
- **Configuration**:
  - Multiple parameter rows (unlimited)
  - Volume-based bounds with upper and lower limits
  - Tiered rate structure

#### 3. Send or Pay Traffic + SRE
- **Value**: `SEND_OR_PAY_TRAFFIC_SINGLE_RATE_EFFECTIVE`
- **Business Purpose**: Traffic-based single rate model with send/pay logic
- **Service Types**: Specific service type combinations
- **Calculation**: Single rate applied to send or pay traffic volumes
- **Use Case**: Traffic-specific discount scenarios with directional considerations
- **Configuration**:
  - Send or pay traffic calculation type
  - Service type restrictions
  - Volume-based bounds

#### 4. Send or Pay Traffic + Stepped/Tiered
- **Value**: `SEND_OR_PAY_TRAFFIC_STEPPED_TIERED`
- **Business Purpose**: Traffic-based tiered model with send/pay logic
- **Parameters**: Multiple tiers for traffic volumes with directional logic
- **Calculation**: Tiered rates applied to send or pay traffic basis
- **Use Case**: Complex traffic-based discounts with volume incentives
- **Configuration**:
  - Multiple parameter rows
  - Send or pay traffic calculation
  - Tiered volume bounds

#### 5. Send or Pay Financial
- **Value**: `SEND_OR_PAY_FINANCIAL`
- **Business Purpose**: Financial commitment model with distribution parameters
- **Features**: Commitment distribution across multiple operator pairs
- **Bounds**: Financial commitment thresholds
- **Use Case**: Financial guarantee scenarios, multi-operator commitments
- **Configuration**:
  - Commitment distribution parameters (minimum 2)
  - Financial bounds only
  - No discount basis value

#### 6. Balanced/Unbalanced (SRE)
- **Value**: `BALANCED_UNBALANCED_SINGLE_RATE_EFFECTIVE`
- **Business Purpose**: Balance-aware single rate model for traffic balancing
- **Balancing Options**: Balanced, Unbalanced, No Balancing
- **Parameters**: Balance-specific calculations with single rate
- **Use Case**: Traffic balance considerations, encouraging balanced traffic
- **Configuration**:
  - Balancing field required
  - Single rate with volume bounds
  - Balance-aware calculations

#### 7. Balanced/Unbalanced (Tiered)
- **Value**: `BALANCED_UNBALANCED_STEPPED_TIERED`
- **Business Purpose**: Balance-aware tiered model for complex balancing
- **Features**: Multi-tier structure with balancing considerations
- **Parameters**: Balance and tier combinations
- **Use Case**: Complex balance-based discounts with volume incentives
- **Configuration**:
  - Multiple parameter rows
  - Balancing field required
  - Tiered structure with balance logic

#### 8. Back to First
- **Value**: `BACK_TO_FIRST`
- **Business Purpose**: Retroactive discount application to first tier
- **Calculation**: Back-to-first tier pricing methodology
- **Parameters**: Retroactive rate adjustments across all volumes
- **Use Case**: Retroactive discount scenarios, volume achievement rewards
- **Configuration**:
  - Back to first calculation type
  - Retroactive rate application
  - Volume-based triggers

#### 9. Per Month Per IMSI
- **Value**: `PER_MONTH_PER_IMSI`
- **Business Purpose**: IMSI-based monthly calculations for data services
- **Bounds**: Unique IMSI count per month
- **Service Types**: Access fee specific (typically data services)
- **Use Case**: IMSI-based pricing models, data service discounts
- **Configuration**:
  - IMSI count bounds
  - Access fee rate
  - Monthly calculation basis

#### 10. Per Month Per IMSI - Above Threshold
- **Value**: `PER_MONTH_PER_IMSI_ABOVE_THRESHOLD`
- **Business Purpose**: Threshold-based IMSI model with financial triggers
- **Features**: Financial threshold support with above-threshold rates
- **Additional Fields**: Above threshold rates for sub-discounts
- **Use Case**: Threshold-triggered IMSI pricing, tiered data services
- **Configuration**:
  - Financial threshold field
  - IMSI count bounds
  - Access fee rate
  - Above threshold rate (sub-discounts only)

#### 11. Per Month Per IMSI - Back to First
- **Value**: `PER_MONTH_PER_IMSI_BACK_TO_FIRST`
- **Business Purpose**: Retroactive IMSI-based model with back-to-first logic
- **Calculation**: Back-to-first methodology applied to IMSI counts
- **Parameters**: IMSI-specific retroactive pricing
- **Use Case**: Complex IMSI retroactive scenarios, data service incentives
- **Configuration**:
  - Back to first calculation for IMSI
  - IMSI count bounds
  - Retroactive rate application

#### 12. Per Month Per IMSI - Stepped/Tiered
- **Value**: `PER_MONTH_PER_IMSI_STEPPED_TIERED`
- **Business Purpose**: Tiered IMSI-based model for progressive pricing
- **Features**: Multiple IMSI count tiers with different rates
- **Parameters**: Tiered IMSI calculations
- **Use Case**: Progressive IMSI-based pricing, volume-based data discounts
- **Configuration**:
  - Multiple parameter rows
  - IMSI count bounds
  - Tiered rate structure

#### 13. Per Month Per IMSI with Incremental Charging
- **Value**: `PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING`
- **Business Purpose**: Incremental IMSI charging model with access fees
- **Features**: Access fee and incremental rates for additional usage
- **Parameters**: Volume included in access fee, incremental charging
- **Use Case**: Incremental IMSI-based charging, bundled data services
- **Configuration**:
  - Access fee rate
  - Incremental rate
  - Volume inclusion in access fee

#### 14. All You Can Eat
- **Value**: `ALL_YOU_CAN_EAT`
- **Business Purpose**: Unlimited usage model with flat financial commitment
- **Settlement**: Credit Note EoA only
- **Bounds**: Financial commitment only (no volume bounds)
- **Use Case**: Unlimited usage scenarios, flat-rate agreements
- **Configuration**:
  - Financial bounds only
  - No discount basis value
  - Fixed settlement method

### Discount Creation Workflow

#### Step 1: Navigate to Agreement Details
- **Route**: `/budgets/{budgetId}/agreements/{agreementId}`
- **Component**: `AgreementDetails`
- **Tab**: Discounts
- **Component**: `Discounts`

#### Step 2: Discount Creation Trigger
- **Button**: "Create Discount"
- **Modal**: `DiscountCreationModal`
- **Component**: `DiscountCreationModalContent`
- **Form**: `DiscountForm`

#### Step 3: Discount Form Structure
```
DiscountCreationModal
├── DiscountCreationModalContent
│   ├── DiscountChilds (Sub-discount Management)
│   └── DiscountForm (Main Form)
│       ├── DiscountModelType (Model Selection)
│       ├── DiscountQualifying (Qualifying Rules - Accordion)
│       ├── AdditionalAttributes (Optional Fields - Accordion)
│       ├── DiscountTraffic (Core Traffic Fields - Accordion)
│       ├── DiscountParameters (Calculation Parameters - Accordion)
│       └── CommitmentDistribution (Financial Distribution - Conditional)
```

#### Step 4: Discount Creation Process Flow
1. **Model Type Selection** → Determines available fields and configurations
2. **Core Traffic Configuration** → Required fields for all discounts
3. **Parameter Configuration** → Model-specific calculation parameters
4. **Optional Configurations** → Additional attributes and qualifying rules
5. **Form Validation** → Real-time validation based on model type
6. **API Submission** → Transform form data and submit to API
7. **Success Handling** → Refresh discount list and show success message

#### Step 5: Sub-Discount Management
- **Availability**: Only after parent discount creation
- **Model Types**: Limited subset of main discount model types
- **Additional Fields**: Rate above commitment, above threshold rate
- **Restrictions**: No inbound market share, financial threshold, or commitment distribution

### Discount Form Fields & Validations

### Core Traffic Fields

#### Home Operators
- **Field**: `home_operators`
- **Type**: Array of operator objects
- **Validation**: Minimum 1 operator required
- **Format**: `{id: number, pmn_code: string}`

#### Partner Operators
- **Field**: `partner_operators`
- **Type**: Array of operator objects
- **Validation**: Minimum 1 operator required
- **Format**: `{id: number, pmn_code: string}`

#### Validity Period
- **Valid From**: `start_date` (Date, required)
- **Valid To**: `end_date` (String, required)
- **Validation**: Date format validation

#### Direction
- **Field**: `direction`
- **Type**: Object with value/title
- **Options**: Inbound, Outbound, Both
- **Validation**: Required selection

#### Service Types
- **Field**: `service_types`
- **Type**: Array of service type objects
- **Validation**: Minimum 1 service type required
- **Options**: Voice, SMS, Data, etc.

#### Settlement & Currency
- **Settlement Method**: `settlement_method` (Required)
- **Currency**: `currency_code` (Required)
- **Tax Type**: `tax_type` (NET/GROSS, Required)
- **Volume Type**: `volume_type` (ACTUAL/BILLED, Required)

### Additional Attributes (Conditional)

#### Call Destinations
- **Field**: `call_destinations`
- **Type**: Array of destination objects
- **Condition**: Available for all discount types
- **Mutual Exclusion**: Cannot be used with Called Countries

#### Called Countries
- **Field**: `called_countries`
- **Type**: Array of country codes
- **Condition**: Available for all discount types
- **Mutual Exclusion**: Cannot be used with Call Destinations

#### Traffic Segments
- **Field**: `traffic_segments`
- **Type**: Array of traffic segment objects
- **Condition**: Available for all discount types
- **Dependency**: Requires home operators selection

#### Rate Above Commitment
- **Field**: `above_commitment_rate`
- **Type**: Number (nullable)
- **Condition**: Only available for sub-discounts
- **Validation**: Rate format (6 integer, 10 decimal digits)

#### Inbound Market Share
- **Field**: `inbound_market_share`
- **Type**: Number (nullable)
- **Condition**: Available based on model type configuration
- **Validation**: Bounds format (15 integer, 5 decimal digits)

#### Financial Threshold
- **Field**: `financial_threshold`
- **Type**: Number (nullable)
- **Condition**: Available based on model type configuration
- **Validation**: Bounds format validation

#### Above Threshold Rate
- **Field**: `above_financial_threshold_rate`
- **Type**: Number (nullable)
- **Condition**: Only available for sub-discounts
- **Validation**: Rate format validation

### Parameter Fields

#### Calculation Type
- **Field**: `calculation_type`
- **Type**: Object with value/title
- **Options**: Varies by model type
- **Validation**: Required, model-specific options

#### Discount Basis
- **Field**: `basis`
- **Type**: Object with value/title
- **Options**: Value, Percentage (model-dependent)
- **Validation**: Model-specific availability

#### Basis Value
- **Field**: `basis_value`
- **Type**: Number (nullable)
- **Condition**: Enabled/disabled based on model type
- **Validation**: Rate format when enabled

#### Bound Type
- **Field**: `bound_type`
- **Type**: Object with value/title
- **Options**: Volume, Financial Commitment, IMSI Count, Market Share
- **Validation**: Model-specific options

#### Lower/Upper Bounds
- **Fields**: `lower_bound`, `upper_bound`
- **Type**: Number (nullable)
- **Condition**: Enabled/disabled based on model type
- **Validation**: Bounds format validation

#### Balancing
- **Field**: `balancing`
- **Type**: Object with value/title
- **Options**: Balanced, Unbalanced, No Balancing
- **Condition**: Available for balanced/unbalanced models

#### Access Fee Rate
- **Field**: `access_fee_rate`
- **Type**: Number (nullable)
- **Condition**: Available for IMSI-based models
- **Validation**: Rate format validation

#### Incremental Rate
- **Field**: `incremental_rate`
- **Type**: Number (nullable)
- **Condition**: Available for incremental charging models
- **Validation**: Rate format validation

### Qualifying Rule Fields

#### Qualifying Direction
- **Field**: `direction` (within qualifying_rule)
- **Type**: Object with value/title
- **Options**: Same as main direction options
- **Validation**: Optional

#### Qualifying Service Types
- **Field**: `service_types` (within qualifying_rule)
- **Type**: Array of service type objects
- **Options**: Same as main service types
- **Validation**: Optional

#### Qualifying Basis
- **Field**: `basis` (within qualifying_rule)
- **Type**: Object with value/title
- **Options**: Volume, Market Share %, IMSI Count, Average Usage
- **Validation**: Optional

#### Qualifying Bounds
- **Fields**: `lower_bound`, `upper_bound` (within qualifying_rule)
- **Type**: Number (nullable)
- **Validation**: Bounds format, lower_bound required if basis selected

### Commitment Distribution Parameters

#### Distribution Operators
- **Home Operators**: Array of operator objects (min 1 required)
- **Partner Operators**: Array of operator objects (min 1 required)
- **Charge**: Number with bounds validation

#### Validation Rules
- **Minimum Quantity**: 2 distribution parameters required
- **Operator Validation**: Each parameter must have operators
- **Charge Validation**: Bounds format validation

### Validation Configurations

#### Rate Validation
- **Integer Part**: Maximum 6 digits
- **Decimal Part**: Maximum 10 digits
- **Format**: Positive numbers only

#### Bounds Validation
- **Integer Part**: Maximum 15 digits
- **Decimal Part**: Maximum 5 digits
- **Format**: Positive numbers only

#### Array Validations
- **Operators**: Minimum 1 item required
- **Service Types**: Minimum 1 item required
- **Parameters**: Model-specific min/max quantities

#### Conditional Validations
- **Model Type Dependent**: Field availability changes
- **Sub-Discount Context**: Different field sets available
- **Mutual Exclusions**: Some fields cannot be used together

## State Management & Data Flow

### Redux Store Structure

#### Root Reducer Composition
The application uses a comprehensive Redux store with the following main state slices:

```javascript
const rootReducer = combineReducers({
  // Core Application State
  appVariables: appVariablesReducer,           // Global app configuration

  // Budget Management State
  budgets: getBudgetsReducer,                  // Budget list data
  createBudget: createBudgetReducer,           // Budget creation state
  budgetParameters: getBudgetParametersReducer, // Budget configuration
  budgetKPIValues: getBudgetKPIValuesReducer,  // Budget calculations
  budgetCountriesValues: getBudgetCountriesValuesReducer, // Country-specific data

  // Agreement Management State
  agreements: getAgreementsReducer,            // Agreement list data
  createAgreement: createAgreementReducer,     // Agreement creation state
  agreementParameters: getAgreementParametersReducer, // Agreement configuration
  agreementKPIValues: getAgreementKPIValuesReducer, // Agreement calculations

  // Discount Management State
  discounts: getDiscountsReducer,              // Discount list data
  createDiscount: createDiscountReducer,       // Discount creation state
  createSubDiscount: createSubDiscountReducer, // Sub-discount creation state
  editDiscount: editDiscountReducer,           // Discount editing state
  editSubDiscount: editSubDiscountReducer,     // Sub-discount editing state
  deleteDiscount: deleteDiscountReducer,       // Discount deletion state

  // Shared Data State
  currencies: getCurrenciesReducer,            // Currency data
  operators: getOperatorsReducer,              // Operator data
  homeOperators: getHomeOperatorsReducer,      // Home operator data
  trafficSegments: getTrafficSegmentsReducer,  // Traffic segment data
  countries: getCountriesReducer,              // Country data

  // UI State
  toastr: toastrReducer,                       // Notification state
  // ... other UI-specific reducers
});
```

#### State Management Patterns

##### 1. Async Action Pattern
```javascript
// Standard async action pattern used throughout the application
const createEntityAction = (entityData) => async (dispatch) => {
  try {
    dispatch(createEntityRequest());           // Set loading state
    const { data } = await createEntity(entityData); // API call
    dispatch(createEntitySuccess(data));      // Store success data
    return data;                              // Return for component use
  } catch (error) {
    dispatch(createEntityFailure(error));     // Store error state
    throw error;                              // Re-throw for component handling
  }
};
```

##### 2. Reducer State Structure
```javascript
// Standard reducer state structure
const initialState = {
  data: null,        // Entity data (object/array)
  isLoading: false,  // Loading state
  error: null,       // Error information
};

const entityReducer = (state = initialState, { type, data, error }) => {
  switch (type) {
    case ENTITY_REQUEST:
      return { ...state, isLoading: true, error: null };
    case ENTITY_SUCCESS:
      return { ...state, data, isLoading: false, error: null };
    case ENTITY_FAILURE:
      return { ...state, isLoading: false, error };
    default:
      return state;
  }
};
```

##### 3. Context Provider Pattern
```javascript
// Context providers complement Redux for component-specific state
const EntityDetailsContextProvider = ({ children }) => {
  const params = useParams();
  const entityData = useSelector(state => state.entity.data);

  const contextValue = useMemo(() => ({
    entityId: params.id,
    entityData,
    // ... other context-specific values
  }), [params.id, entityData]);

  return (
    <EntityDetailsContext.Provider value={contextValue}>
      {children}
    </EntityDetailsContext.Provider>
  );
};
```

### Data Flow Patterns

#### 1. Component → Redux → API → Redux → Component
```
User Interaction → Component Event Handler → Dispatch Action → API Service →
Redux Store Update → Component Re-render via useSelector
```

#### 2. Context-Enhanced Data Flow
```
Redux Global State → Context Provider → Child Components →
Local State Updates → Context Updates → Child Component Re-renders
```

#### 3. Form Data Flow
```
Form Input → Formik State → Validation (Yup) → Transform Data →
API Submission → Redux Store Update → UI Update
```

### Context Providers

#### BudgetDetailsContextProvider
```javascript
const BudgetDetailsContext = {
  budgetId,                           // Current budget ID from URL params
  globalBudgetFilters,                // Shared filters across budget components
  fullGlobalBudgetFilters,            // Complete filter state
  resetFullGlobalBudgetFilters,       // Filter reset function
  isFullGlobalBudgetFiltersSaved,     // Filter save state
  updateFullGlobalBudgetFilters,      // Filter update function
  triggerBudgetCalculationModal,      // Calculation modal trigger
  setTriggerBudgetCalculationModal,   // Calculation modal setter
  countryView,                        // Country-specific view state
  setCountryView,                     // Country view setter
  isGlobalDateFilterModified,         // Date filter modification tracking
  setIsGlobalDateFilterModified,      // Date filter modification setter
  isGlobalFiltersReset,               // Filter reset tracking
  setIsGlobalFiltersReset,            // Filter reset setter
};
```

#### AgreementDetailsContextProvider
```javascript
const AgreementDetailsContext = {
  agreementId,                        // Current agreement ID from URL params
  budgetId,                          // Parent budget ID from URL params
  agreementParameters,               // Agreement configuration (IDs only)
  agreementParametersData,           // Full agreement data from Redux
  globalAgreementFilters,            // Agreement-specific filters
  setGlobalAgreementFilters,         // Agreement filter setter
  agreementsComparisonData,          // Agreement comparison state
  setAgreementsComparisonData,       // Agreement comparison setter
  triggerBudgetCalculationModal,     // Calculation modal trigger
  setTriggerBudgetCalculationModal,  // Calculation modal setter
};
```

## Navigation & Routing

### Route Configuration

#### Primary Routes
```javascript
const routesConfig = [
  {
    key: 'budgetListPage',
    path: '/budgets',                    // Budget list page
    component: BudgetListContainer,
  },
  {
    key: 'budgetPage',
    path: '/budgets/:id',               // Budget details page
    component: BudgetDetails,
  },
  {
    key: 'agreementPage',
    path: '/budgets/:budgetId/agreements/:agreementId', // Agreement details page
    component: AgreementDetails,
  },
  {
    key: 'iotRatesListPage',
    path: '/iot-rates',                 // IOT rates page
    component: IOTRatesListContainer,
  },
  {
    key: 'notFoundPage',
    path: '*',                          // 404 page
    component: PageNotFound,
  },
];
```

#### Route Helper Functions
```javascript
// Path generation utilities
export const getBudgetDetailsPath = (id) => `/budgets/${id}`;
export const getAgreementDetailsPath = (budgetId, agreementId) =>
  `/budgets/${budgetId}/agreements/${agreementId}`;
```

### Navigation Patterns

#### 1. Hierarchical Navigation
```
Budget List (/budgets)
    ↓ Click Budget
Budget Details (/budgets/:id)
    ↓ Click Agreement
Agreement Details (/budgets/:budgetId/agreements/:agreementId)
```

#### 2. Modal-Based Operations
- **Budget Creation**: Modal on Budget List page
- **Agreement Creation**: Modal on Budget Details page
- **Discount Creation/Editing**: Modal on Agreement Details page
- **Sub-Discount Management**: Nested modal within discount operations

#### 3. Context Preservation During Navigation
- **URL Parameters**: Preserve entity IDs in URL
- **Context Providers**: Maintain entity-specific state
- **Redux State**: Persist data across navigation
- **Form State**: Preserve form data during modal operations

#### 4. Breadcrumb Navigation
- **Dynamic Breadcrumbs**: Generated based on current route
- **Entity Names**: Display actual entity names in breadcrumbs
- **Navigation Links**: Clickable breadcrumb items for quick navigation

## API Endpoints & Data Flow

### API Service Architecture

#### HTTP Service Configuration
```javascript
// Core HTTP service with interceptors
const HTTPService = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for authentication
HTTPService.interceptors.request.use(
  (config) => {
    const token = getAuthToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
HTTPService.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle authentication errors
      redirectToLogin();
    }
    return Promise.reject(error);
  }
);
```

### Base URLs and Structure
- **Base API Path**: `/budget-agreements/{agreementId}/discounts`
- **Sub-Discount Path**: `/budget-agreements/{agreementId}/discounts/{parentDiscountId}/sub-discounts`
- **HTTP Client**: Axios with query string serialization
- **Parameter Serialization**: `qs.stringify(params, { arrayFormat: 'repeat' })`

### Core API Endpoints

#### 1. Get Discounts
- **Method**: `GET`
- **URL**: `/budget-agreements/{budgetAgreementId}/discounts`
- **Purpose**: Retrieve all discounts for an agreement
- **Parameters**:
  - Query parameters for filtering
  - Signal for request cancellation
- **Response**: Array of discount objects
- **Service**: `getDiscounts(signal, budgetAgreementId, data)`

#### 2. Create Discount
- **Method**: `POST`
- **URL**: `/budget-agreements/{id}/discounts`
- **Purpose**: Create a new discount
- **Request Body**: Transformed form data
- **Response**: Created discount object
- **Service**: `createDiscount(id, data)`
- **Action**: `createDiscountAction(id, requestData)`

#### 3. Edit Discount
- **Method**: `PATCH`
- **URL**: `/budget-agreements/{id}/discounts/{discountId}`
- **Purpose**: Update existing discount
- **Request Body**: Transformed form data
- **Response**: Updated discount object
- **Service**: `editDiscount(id, discountId, data)`
- **Action**: `editDiscountAction(id, discountId, requestData)`

#### 4. Delete Discount
- **Method**: `DELETE`
- **URL**: `/budget-agreements/{agreementId}/discounts/{discountId}`
- **Purpose**: Delete a discount
- **Response**: Deletion confirmation
- **Service**: `deleteDiscount(agreementId, discountId)`
- **Action**: `deleteDiscountAction(agreementId, discountId)`

#### 5. Create Sub-Discount
- **Method**: `POST`
- **URL**: `/budget-agreements/{agreementId}/discounts/{parentDiscountId}/sub-discounts`
- **Purpose**: Create a sub-discount under a parent discount
- **Request Body**: Transformed form data
- **Response**: Created sub-discount object
- **Service**: `createSubDiscount(agreementId, parentDiscountId, data)`
- **Action**: `createSubDiscountAction(agreementId, parentDiscountId, requestData)`

#### 6. Edit Sub-Discount
- **Method**: `PATCH`
- **URL**: `/budget-agreements/{agreementId}/discounts/{parentDiscountId}/sub-discounts/{subDiscountId}`
- **Purpose**: Update existing sub-discount
- **Request Body**: Transformed form data
- **Response**: Updated sub-discount object
- **Service**: `editSubDiscount(agreementId, parentDiscountId, subDiscountId, data)`
- **Action**: `editSubDiscountAction(agreementId, parentDiscountId, subDiscountId, requestData)`

### Agreement Management APIs

#### 1. Get Agreements
- **Method**: `GET`
- **URL**: `/budgets/{budgetId}/agreements/`
- **Purpose**: Retrieve all agreements for a budget
- **Parameters**: Budget ID and filter parameters
- **Service**: `getAgreements(signal, data)`

#### 2. Create Agreement
- **Method**: `POST`
- **URL**: `/budgets/{budgetId}/agreements`
- **Purpose**: Create a new agreement
- **Service**: `createAgreement(budgetId, params)`

#### 3. Activate All Agreements
- **Method**: `POST`
- **URL**: `/budgets/{budgetId}/agreements/activated/bulk`
- **Purpose**: Bulk activate agreements
- **Service**: `activateAllAgreements(budgetId)`

#### 4. Deactivate All Agreements
- **Method**: `POST`
- **URL**: `/budgets/{budgetId}/agreements/deactivated/bulk`
- **Purpose**: Bulk deactivate agreements
- **Service**: `deactivateAllAgreements(budgetId)`

### Data Transformation Utilities

#### Form to API Data Conversion
**Function**: `getConvertedForRequestFormData(formData)`

**Transformations**:
```javascript
// Model Type
[discountModelTypeField]: formData[discountModelTypeField].value

// Operators (Extract IDs)
[discountFields.homeOperators]: getIds(formData[discountFields.homeOperators])
[discountFields.partnerOperators]: getIds(formData[discountFields.partnerOperators])

// Dates
[discountFields.validFrom]: formData[discountFields.validFrom]
[discountFields.validTo]: formData[discountFields.validTo]

// Single Value Objects
[discountFields.discountDirection]: formData[discountFields.discountDirection].value
[discountFields.discountSettlementMethod]: formData[discountFields.discountSettlementMethod].value
[discountFields.discountCurrency]: formData[discountFields.discountCurrency].code

// Arrays (Extract Values)
[discountFields.serviceTypes]: getArrayByKey(formData[discountFields.serviceTypes], 'value')
[discountFields.callDestinations]: getArrayByKey(formData[discountFields.callDestinations], 'value')
[discountFields.trafficSegments]: getArrayByKey(formData[discountFields.trafficSegments], 'id')

// Numeric Fields (Convert and Validate)
[discountFields.rateAboveCommitment]: getConvertedFormattedNumber(value)
[discountFields.inboundMarketShare]: getConvertedFormattedNumber(value)
[discountFields.financialThreshold]: getConvertedFormattedNumber(value)
[discountFields.aboveThresholdRate]: getConvertedFormattedNumber(value)

// Parameters Array
parameters: formData.parameters.map((row) => ({
  [discountParametersFields.calculationType]: row[field]?.value,
  [discountParametersFields.discountBasis]: row[field]?.value,
  [discountParametersFields.discountBasisValue]: getConvertedFormattedNumber(row[field]),
  [discountParametersFields.boundType]: row[field]?.value,
  [discountParametersFields.lowerBound]: getConvertedFormattedNumber(row[field]),
  [discountParametersFields.upperBound]: getConvertedFormattedNumber(row[field]),
  [discountParametersFields.balancing]: row[field]?.value,
  [discountParametersFields.accessFeeRate]: getConvertedFormattedNumber(row[field]),
  [discountParametersFields.incrementalRate]: getConvertedFormattedNumber(row[field])
}))

// Qualifying Rule (Conditional)
[discountQualifyingRule]: isDiscountQualifyingRequiredFieldsEmpty(formData[discountQualifyingRule])
  ? null
  : {
      [discountQualifyingFields.direction]: formData[rule][field]?.value,
      [discountQualifyingFields.serviceTypes]: getArrayByKey(formData[rule][field], 'value'),
      [discountQualifyingFields.basis]: formData[rule][field]?.value,
      [discountQualifyingFields.lowerBound]: getConvertedFormattedNumber(formData[rule][field]),
      [discountQualifyingFields.upperBound]: getConvertedFormattedNumber(formData[rule][field])
    }

// Commitment Distribution Parameters (Conditional)
[commitmentDistributionParameters]: formData[commitmentDistributionParameters]?.map((row) => ({
  [commitmentDistributionParametersFields.homeOperators]: getIds(row[field]),
  [commitmentDistributionParametersFields.partnerOperators]: getIds(row[field]),
  [commitmentDistributionParametersFields.charge]: getConvertedFormattedNumber(row[field])
})) || null
```

#### API to Form Data Conversion
**Function**: `useInitialDiscountFormValues(discountDataFromRequest)`

**Transformations**:
```javascript
// Single Value Conversions
getConvertedSingleData({
  field: discountModelTypeField,
  config: availableDiscountModelTypes,
  requestData: discountDataFromRequest
})

// Multiple Value Conversions
getConvertedMultipleData({
  field: discountFields.serviceTypes,
  config: serviceTypeConfig,
  requestData: discountDataFromRequest
})

// Currency Conversion
[discountFields.discountCurrency]: currencies.find(
  ({ code }) => code === discountDataFromRequest[discountFields.discountCurrency]
)

// Numeric Field Truncation
[discountParametersFields.lowerBound]: truncateZero(parameter[field])
[discountParametersFields.upperBound]: truncateZero(parameter[field])

// Qualifying Rule Conversion
getConvertedDiscountQualifyingRuleData(): isNull(discountDataFromRequest[discountQualifyingRule])
  ? defaultDiscountQualifyingValues
  : convertedQualifyingRuleData

// Parameters Array Conversion
parameters: discountDataFromRequest.parameters?.map((parameter) => ({
  ...parameter,
  [discountParametersFields.calculationType]: getConvertedSingleData({...}),
  [discountParametersFields.discountBasis]: getConvertedSingleData({...}),
  [discountParametersFields.boundType]: getConvertedSingleData({...}),
  [discountParametersFields.balancing]: getConvertedSingleData({...}),
  // Numeric fields with truncation
  [discountParametersFields.lowerBound]: truncateZero(parameter[field]),
  [discountParametersFields.upperBound]: truncateZero(parameter[field])
}))
```

### Error Handling

#### API Error Responses
```javascript
// Standard Error Handling
try {
  await dispatch(createDiscountAction(agreementId, formData));
  dispatch(showSuccessToastr(toastrMessages.successCreation));
} catch (e) {
  const errorToastrText = e.response.data?.detail
    ? e.response.data?.detail
    : toastrMessages.errorCreation;
  dispatch(showErrorToastr(errorToastrText));
}

// Sub-Discount Error Handling
export const getErrorMessage = (e) => {
  let errorMessage = toastrMessages.errorCreation;

  if (e.response.data?.detail) {
    errorMessage = e.response.data?.detail;
  } else if (e.response.data?.length) {
    [errorMessage] = e.response.data;
  }

  return errorMessage;
};
```

#### Redux Action Patterns
```javascript
// Standard Action Pattern
const createDiscountAction = (id, requestData) => async (dispatch) => {
  try {
    dispatch(createDiscountRequest());
    const { data } = await createDiscount(id, requestData);
    dispatch(createDiscountSuccess(data));
    return data;
  } catch (error) {
    dispatch(createDiscountFailure(error));
    throw error;
  }
};
```

### Data Flow Sequence

#### 1. Discount Creation Flow
1. User fills form → Form validation (Yup schema)
2. Form submission → `getConvertedForRequestFormData(formData)`
3. API call → `createDiscountAction(agreementId, transformedData)`
4. Success → Refresh discount list + Success toast
5. Error → Display error toast with API message

#### 2. Discount Editing Flow
1. Load existing data → `useInitialDiscountFormValues(discountData)`
2. Form initialization → Convert API data to form format
3. User edits → Real-time validation
4. Form submission → Transform and submit
5. Success → Refresh + Success notification

#### 3. Sub-Discount Management Flow
1. Parent discount selection → Enable sub-discount creation
2. Sub-discount form → Limited model types available
3. Additional attributes → Different field sets for sub-discounts
4. API submission → Nested endpoint structure
5. Hierarchy update → Refresh parent discount data

### Request/Response Examples

#### Create Discount Request
```json
{
  "model_type": "SINGLE_RATE_EFFECTIVE",
  "home_operators": [36488],
  "partner_operators": [36489],
  "start_date": "2025-01-01",
  "end_date": "2025-12-31",
  "direction": "INBOUND",
  "service_types": ["VOICE", "SMS"],
  "settlement_method": "CREDIT_NOTE_EOA",
  "currency_code": "USD",
  "tax_type": "NET",
  "volume_type": "ACTUAL",
  "parameters": [{
    "calculation_type": "SINGLE_RATE_EFFECTIVE",
    "basis": "VALUE",
    "basis_value": 0.05,
    "bound_type": "VOLUME",
    "lower_bound": 1000,
    "upper_bound": null
  }],
  "qualifying_rule": null,
  "commitment_distribution_parameters": null,
  "sub_discounts": null
}
```

#### Get Discounts Response
```json
[
  {
    "id": 9819,
    "agreement_id": 6145,
    "model_type": "SINGLE_RATE_EFFECTIVE",
    "home_operators": [{"id": 36488, "pmn_code": "UKRAS"}],
    "partner_operators": [{"id": 36489, "pmn_code": "USATT"}],
    "start_date": "2025-01-01",
    "end_date": "2025-12-31",
    "direction": "INBOUND",
    "service_types": ["VOICE", "SMS"],
    "settlement_method": "CREDIT_NOTE_EOA",
    "currency_code": "USD",
    "tax_type": "NET",
    "volume_type": "ACTUAL",
    "parameters": [...],
    "qualifying_rule": null,
    "commitment_distribution_parameters": null,
    "sub_discounts": [...]
  }
]
```

### Data Transformation Patterns

#### Form Data to API Format
```javascript
const transformFormToAPI = (formData) => {
  return {
    // Transform field names (camelCase → snake_case)
    model_type: formData.modelType,
    service_types: formData.serviceTypes,
    discount_basis: formData.discountBasis,

    // Transform nested objects
    parameters: formData.parameters.map(param => ({
      rate: param.rate,
      volume_lower_bound: param.volumeLowerBound,
      volume_upper_bound: param.volumeUpperBound,
      financial_lower_bound: param.financialLowerBound,
      financial_upper_bound: param.financialUpperBound,
    })),

    // Transform arrays and complex structures
    qualifying_rules: transformQualifyingRules(formData.qualifyingRules),
    additional_attributes: transformAdditionalAttributes(formData.additionalAttributes),
  };
};
```

#### API Response to Form Format
```javascript
const transformAPIToForm = (apiData) => {
  return {
    // Transform field names (snake_case → camelCase)
    modelType: apiData.model_type,
    serviceTypes: apiData.service_types,
    discountBasis: apiData.discount_basis,

    // Transform nested structures
    parameters: apiData.parameters.map(param => ({
      rate: param.rate,
      volumeLowerBound: param.volume_lower_bound,
      volumeUpperBound: param.volume_upper_bound,
      financialLowerBound: param.financial_lower_bound,
      financialUpperBound: param.financial_upper_bound,
    })),

    // Handle optional fields with defaults
    qualifyingRules: apiData.qualifying_rules || [],
    additionalAttributes: apiData.additional_attributes || {},
  };
};
```

## Component Architecture

### Component Hierarchy

#### Budget Level Components
```
BudgetListContainer
├── BudgetList (Display Component)
├── GetBudgets (Data Fetching)
├── BudgetCreation (Modal)
│   ├── BudgetCreationModal
│   ├── BudgetCreationModalContent
│   │   ├── BudgetCreationMainForm
│   │   ├── CreateBudget (Logic Component)
│   │   └── CreateDefaultForecastRules
│   └── BudgetCreationModalActions
└── BudgetCalculation (Shared Feature)

BudgetDetails
├── BudgetDetailsContextProvider (Context)
├── BudgetParameters (Configuration)
├── BudgetItems (Tab Container)
│   ├── Agreements (Tab Content)
│   │   ├── CreateAgreement (Modal)
│   │   ├── AgreementsTable (Display)
│   │   ├── ActivateAllAgreements (Bulk Actions)
│   │   └── GetAgreements (Data Fetching)
│   └── ForecastRules (Tab Content)
└── BudgetValues (Calculations & KPIs)
```

#### Agreement Level Components
```
AgreementDetails
├── AgreementDetailsContextProvider (Context)
├── AgreementParameters (Configuration)
├── AgreementItems (Tab Container)
│   ├── Discounts (Tab Content)
│   │   ├── DiscountsTable (Display)
│   │   ├── GetDiscounts (Data Fetching)
│   │   ├── DiscountCreation (Modal)
│   │   ├── DiscountEditing (Modal)
│   │   ├── DiscountDeleting (Confirmation)
│   │   ├── SubDiscountCreation (Modal)
│   │   ├── SubDiscountEditing (Modal)
│   │   └── shared/ (Shared Components)
│   │       ├── DiscountForm (Main Form)
│   │       │   ├── DiscountModelType
│   │       │   ├── DiscountTraffic
│   │       │   ├── DiscountParameters
│   │       │   ├── DiscountQualifying
│   │       │   └── AdditionalAttributes
│   │       └── DiscountChilds (Sub-discount Management)
│   └── ForecastRules (Tab Content)
└── AgreementValues (Calculations & KPIs)
```

### Component Design Patterns

#### 1. Container-Presentation Pattern
```javascript
// Container Component (Logic)
const BudgetListContainer = () => {
  const dispatch = useDispatch();
  const budgets = useSelector(state => state.budgets.data);
  const isLoading = useSelector(state => state.budgets.isLoading);

  useEffect(() => {
    dispatch(getBudgetsAction());
  }, [dispatch]);

  const handleCreateBudget = (budgetData) => {
    return dispatch(createBudgetAction(budgetData));
  };

  return (
    <BudgetList
      budgets={budgets}
      isLoading={isLoading}
      onCreateBudget={handleCreateBudget}
    />
  );
};

// Presentation Component (UI)
const BudgetList = ({ budgets, isLoading, onCreateBudget }) => {
  return (
    <div>
      <BudgetCreationButton onClick={onCreateBudget} />
      {isLoading ? <LoadingSpinner /> : <BudgetTable budgets={budgets} />}
    </div>
  );
};
```

#### 2. Modal Component Pattern
```javascript
const DiscountCreationModal = ({ isOpen, onClose, agreementId }) => {
  const [formData, setFormData] = useState(initialFormData);
  const dispatch = useDispatch();

  const handleSubmit = async (values) => {
    try {
      await dispatch(createDiscountAction(agreementId, values));
      onClose();
      // Refresh discount list
      dispatch(getDiscountsAction(agreementId));
    } catch (error) {
      // Handle error
    }
  };

  return (
    <Modal open={isOpen} onClose={onClose}>
      <DiscountForm
        initialValues={formData}
        onSubmit={handleSubmit}
        onCancel={onClose}
      />
    </Modal>
  );
};
```

#### 3. Context Provider Pattern
```javascript
const BudgetDetailsContextProvider = ({ children }) => {
  const { id: budgetId } = useParams();
  const [globalBudgetFilters, setGlobalBudgetFilters] = useState({});
  const [triggerCalculationModal, setTriggerCalculationModal] = useState(false);

  const contextValue = useMemo(() => ({
    budgetId,
    globalBudgetFilters,
    setGlobalBudgetFilters,
    triggerCalculationModal,
    setTriggerCalculationModal,
  }), [budgetId, globalBudgetFilters, triggerCalculationModal]);

  return (
    <BudgetDetailsContext.Provider value={contextValue}>
      {children}
    </BudgetDetailsContext.Provider>
  );
};
```

#### 4. Data Fetching Component Pattern
```javascript
const GetDiscounts = ({ agreementId, filters, children }) => {
  const dispatch = useDispatch();
  const discounts = useSelector(state => state.discounts.data);
  const isLoading = useSelector(state => state.discounts.isLoading);
  const error = useSelector(state => state.discounts.error);

  useEffect(() => {
    const abortController = new AbortController();

    dispatch(getDiscountsAction(agreementId, filters, abortController.signal));

    return () => abortController.abort();
  }, [dispatch, agreementId, filters]);

  return children({ discounts, isLoading, error });
};

// Usage
<GetDiscounts agreementId={agreementId} filters={filters}>
  {({ discounts, isLoading, error }) => (
    <DiscountsTable
      discounts={discounts}
      isLoading={isLoading}
      error={error}
    />
  )}
</GetDiscounts>
```

## Form Validation & Error Handling

### Formik Integration

#### Form Setup Pattern
```javascript
const DiscountForm = ({ initialValues, onSubmit, onCancel }) => {
  return (
    <Formik
      initialValues={initialValues}
      validationSchema={getValidationSchema}
      onSubmit={onSubmit}
      enableReinitialize
    >
      {({ values, errors, touched, setFieldValue, isSubmitting }) => (
        <Form>
          <DiscountFormFields
            values={values}
            errors={errors}
            touched={touched}
            setFieldValue={setFieldValue}
          />
          <FormActions
            onCancel={onCancel}
            isSubmitting={isSubmitting}
          />
        </Form>
      )}
    </Formik>
  );
};
```

#### Dynamic Validation Schema
```javascript
const getValidationSchema = (values) => {
  const baseSchema = {
    modelType: Yup.string().required('Model type is required'),
    serviceTypes: Yup.array().min(1, 'At least one service type is required'),
  };

  // Add conditional validations based on model type
  if (values.modelType === 'SINGLE_RATE_EFFECTIVE') {
    baseSchema.parameters = Yup.array().of(
      Yup.object().shape({
        rate: Yup.number().required('Rate is required').min(0),
        volumeLowerBound: Yup.number().min(0),
        volumeUpperBound: Yup.number().min(0),
      })
    );
  }

  // Add model-specific validations
  if (isInboundMarketShareRequired(values.modelType)) {
    baseSchema.isInboundMarketShare = Yup.boolean().required();
  }

  return Yup.object().shape(baseSchema);
};
```

### Error Handling Patterns

#### API Error Handling
```javascript
const handleAPIError = (error) => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;

    switch (status) {
      case 400:
        return {
          type: 'VALIDATION_ERROR',
          message: data.message || 'Invalid request data',
          fields: data.errors || {},
        };
      case 401:
        return {
          type: 'AUTHENTICATION_ERROR',
          message: 'Authentication required',
        };
      case 403:
        return {
          type: 'AUTHORIZATION_ERROR',
          message: 'Access denied',
        };
      case 404:
        return {
          type: 'NOT_FOUND_ERROR',
          message: 'Resource not found',
        };
      case 500:
        return {
          type: 'SERVER_ERROR',
          message: 'Internal server error',
        };
      default:
        return {
          type: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
        };
    }
  } else if (error.request) {
    // Network error
    return {
      type: 'NETWORK_ERROR',
      message: 'Network connection error',
    };
  } else {
    // Other error
    return {
      type: 'CLIENT_ERROR',
      message: error.message || 'An error occurred',
    };
  }
};
```

#### Form Error Display
```javascript
const FormField = ({ name, label, type = 'text', ...props }) => {
  const [field, meta] = useField(name);
  const hasError = meta.touched && meta.error;

  return (
    <FormControl error={hasError} fullWidth>
      <InputLabel>{label}</InputLabel>
      <Input
        {...field}
        {...props}
        type={type}
      />
      {hasError && (
        <FormHelperText>{meta.error}</FormHelperText>
      )}
    </FormControl>
  );
};
```

## Conditional Logic & Business Rules

### Model Type-Based Field Configuration

Each discount model type has a specific configuration that determines:
- Available settlement methods
- Service type combinations
- Parameter configurations
- Additional field availability
- Validation rules

#### Configuration Structure
```javascript
const configForModelType = {
  availableValues: {
    [discountFields.discountSettlementMethod]: [...], // Available settlement methods
    [discountFields.serviceTypes]: {...}, // Service type configuration
    parameters: [...], // Parameter configurations
    [discountFields.inboundMarketShare]: boolean, // Field availability
    [discountFields.financialThreshold]: boolean // Field availability
  },
  maxParametersQuantity: number, // Maximum parameter rows
  minParametersQuantity: number, // Minimum parameter rows
  additionalFields: {...} // Additional field configurations
};
```

### Model-Specific Configurations

#### Single Rate Effective (SRE)
```javascript
availableValues: {
  [discountFields.discountSettlementMethod]: [discountSettlementMethodOptions.creditNoteEoA],
  [discountFields.serviceTypes]: {
    getAvailableConfig: getAllServiceTypesCombinations,
    enableSelectAll: true
  },
  parameters: [{
    [discountParametersFields.calculationType]: [calculationTypesOptions.singleRateEffective],
    [discountParametersFields.discountBasis]: [discountBasisOptions.value],
    [discountParametersFields.discountBasisValue]: { isDisabled: false },
    [discountParametersFields.boundType]: [boundTypeOptions.volume],
    [discountParametersFields.lowerBound]: { isDisabled: false },
    [discountParametersFields.upperBound]: { isDisabled: false }
  }]
},
maxParametersQuantity: 1,
minParametersQuantity: 1
```

#### All You Can Eat
```javascript
availableValues: {
  [discountFields.discountSettlementMethod]: [discountSettlementMethodOptions.creditNoteEoA],
  [discountFields.serviceTypes]: {
    getAvailableConfig: getAllServiceTypesCombinations,
    enableSelectAll: true
  },
  parameters: [{
    [discountParametersFields.calculationType]: [calculationTypesOptions.allYouCanEat],
    [discountParametersFields.discountBasis]: [], // Empty - not available
    [discountParametersFields.discountBasisValue]: { isDisabled: true },
    [discountParametersFields.boundType]: [boundTypeOptions.financial],
    [discountParametersFields.lowerBound]: { isDisabled: false },
    [discountParametersFields.upperBound]: { isDisabled: true }
  }]
},
maxParametersQuantity: 1,
minParametersQuantity: 1
```

#### Send or Pay Financial
```javascript
availableValues: {
  [discountFields.discountSettlementMethod]: [discountSettlementMethodOptions.creditNoteEoA],
  [discountFields.serviceTypes]: {
    getAvailableConfig: getAllServiceTypesCombinations,
    enableSelectAll: true
  },
  parameters: [{
    [discountParametersFields.calculationType]: [calculationTypesOptions.sendOrPayFinancial],
    [discountParametersFields.discountBasis]: [],
    [discountParametersFields.discountBasisValue]: { isDisabled: true },
    [discountParametersFields.boundType]: [boundTypeOptions.financial],
    [discountParametersFields.lowerBound]: { isDisabled: false },
    [discountParametersFields.upperBound]: { isDisabled: true }
  }]
},
maxParametersQuantity: 1,
minParametersQuantity: 1,
additionalFields: {
  [commitmentDistributionParameters]: true // Enables commitment distribution
}
```

#### Per Month Per IMSI - Above Threshold
```javascript
availableValues: {
  [discountFields.discountSettlementMethod]: [discountSettlementMethodOptions.creditNoteEoA],
  [discountFields.serviceTypes]: {
    getAvailableConfig: getAccessFeeServiceType,
    enableSelectAll: false
  },
  parameters: [{
    [discountParametersFields.calculationType]: [calculationTypesOptions.perMonthPerIMSIAboveThreshold],
    [discountParametersFields.discountBasis]: [discountBasisOptions.value],
    [discountParametersFields.discountBasisValue]: { isDisabled: false },
    [discountParametersFields.boundType]: [boundTypeOptions.uniqueIMSICountPerMonth],
    [discountParametersFields.lowerBound]: { isDisabled: false },
    [discountParametersFields.upperBound]: { isDisabled: true },
    [discountParametersFields.accessFeeRate]: { isDisabled: false }
  }]
},
maxParametersQuantity: 1,
minParametersQuantity: 1,
additionalFields: {
  [discountFields.financialThreshold]: true // Enables financial threshold field
}
```

### Sub-Discount Restrictions

#### Available Model Types for Sub-Discounts
```javascript
export const availableSubDiscountModelTypes = [
  modelTypesOptions.singleRateEffective,
  modelTypesOptions.steppedTiered,
  modelTypesOptions.backToFirst,
  modelTypesOptions.perMonthPerIMSI,
  modelTypesOptions.perMonthPerIMSIAboveThreshold,
  modelTypesOptions.perMonthPerIMSIBackToFirst,
  modelTypesOptions.perMonthPerIMSISteppedTiered,
  modelTypesOptions.perMonthPerIMSIWithIncrementalCharging
];
```

#### Sub-Discount Field Differences
- **Rate Above Commitment**: Only available for sub-discounts
- **Above Threshold Rate**: Only available for sub-discounts
- **Inbound Market Share**: NOT available for sub-discounts
- **Financial Threshold**: NOT available for sub-discounts
- **Commitment Distribution**: NOT available for sub-discounts

### Additional Attributes Conditional Logic

#### Configuration Function
```javascript
export const getDefaultAdditionalAttributesConfig = ({
  formik,
  isSubDiscount,
  isInboundMarketShare,
  isFinancialThreshold
}) => {
  const defaultConfig = {
    callDestinations: { /* Always available */ },
    trafficSegments: { /* Always available */ }
  };

  const conditionalConfig = {
    // Only for sub-discounts
    ...(isSubDiscount && {
      rateAboveCommitment: { /* Sub-discount only */ },
      aboveThresholdRate: { /* Sub-discount only */ }
    }),

    // Only for main discounts with specific model types
    ...(!isSubDiscount && isInboundMarketShare && {
      inboundMarketShare: { /* Main discount only */ }
    }),

    ...(!isSubDiscount && isFinancialThreshold && {
      financialThreshold: { /* Main discount only */ }
    })
  };

  return { ...defaultConfig, ...conditionalConfig };
};
```

#### Field Availability Matrix

| Field | Main Discount | Sub-Discount | Condition |
|-------|---------------|--------------|-----------|
| Call Destinations | ✅ | ✅ | Always available |
| Traffic Segments | ✅ | ✅ | Always available |
| Rate Above Commitment | ❌ | ✅ | Sub-discount only |
| Above Threshold Rate | ❌ | ✅ | Sub-discount only |
| Inbound Market Share | ✅* | ❌ | Model type dependent |
| Financial Threshold | ✅* | ❌ | Model type dependent |

*Depends on model type configuration

### Service Type Combinations

#### Service Type Configuration Types
```javascript
// All combinations available with select all option
{
  getAvailableConfig: getAllServiceTypesCombinations,
  enableSelectAll: true
}

// Default combinations without select all
{
  getAvailableConfig: getDefaultServiceTypesCombinationBasedOnValue,
  enableSelectAll: false
}

// Access fee specific (IMSI models)
{
  getAvailableConfig: getAccessFeeServiceType,
  enableSelectAll: false
}
```

#### Service Type Mutual Exclusions
- **Call Destinations** and **Called Countries** are mutually exclusive
- When one is selected, the other becomes disabled
- Clearing one enables the other

### Parameter Field Dependencies

#### Basis Value Dependency
```javascript
[discountParametersFields.discountBasisValue]: {
  isDisabled: !discountBasis || discountBasis.length === 0
}
```

#### Bound Dependencies
```javascript
// Lower bound typically required when bound type is selected
[discountParametersFields.lowerBound]: {
  isDisabled: !boundType
}

// Upper bound availability varies by model
[discountParametersFields.upperBound]: {
  isDisabled: modelType !== 'STEPPED_TIERED'
}
```

#### Access Fee and Incremental Rate Dependencies
```javascript
// Access fee rate - only for IMSI-based models
[discountParametersFields.accessFeeRate]: {
  isDisabled: !isIMSIBasedModel(calculationType)
}

// Incremental rate - only for incremental charging models
[discountParametersFields.incrementalRate]: {
  isDisabled: calculationType !== 'PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING'
}
```

### Qualifying Rule Conditional Logic

#### Qualifying Rule States
```javascript
export const discountQualifyingStates = {
  empty: 'EMPTY',           // No fields filled
  partiallyFilled: 'PARTIALLY_FILLED', // Some fields filled
  filled: 'FILLED'          // All required fields filled
};
```

#### Required Fields for Qualifying Rule
```javascript
export const discountQualifyingRequiredFields = [
  discountQualifyingFields.direction,
  discountQualifyingFields.serviceTypes,
  discountQualifyingFields.basis,
  discountQualifyingFields.lowerBound
];
```

#### Qualifying Rule Validation
- If any required field is filled, all required fields become mandatory
- Upper bound is optional
- Empty qualifying rule is converted to null in API request

### Commitment Distribution Conditional Logic

#### Availability Condition
```javascript
// Only available for specific model types
additionalFields: {
  [commitmentDistributionParameters]: true
}
```

#### Minimum Requirements
```javascript
export const minCommitmentDistributionQuantity = 2;
```

#### Validation Rules
- Minimum 2 distribution parameters required
- Each parameter must have home and partner operators
- Charge field is required for each parameter
- Cannot be used with sub-discounts

### Dynamic Form Behavior

#### Model Type Change Impact
When model type changes:
1. **Parameters reset** to default configuration for new model
2. **Additional fields** availability recalculated
3. **Service types** configuration updated
4. **Settlement methods** filtered to available options
5. **Validation schema** updated for new constraints

#### Form State Management
```javascript
const useDiscountFormConfigByModelType = (modelType) => {
  const discountFormConfig = discountFormConfigByModelType[modelType?.value];

  return {
    discountFormConfig,
    availableValues: discountFormConfig?.availableValues || {},
    maxParametersQuantity: discountFormConfig?.maxParametersQuantity || 1,
    minParametersQuantity: discountFormConfig?.minParametersQuantity || 1,
    additionalFields: discountFormConfig?.additionalFields || {}
  };
};
```

#### Field Reset Logic
```javascript
// When additional attribute is disabled
resetFieldsValues: () => formik.setValues((values) => ({
  ...values,
  [discountFields.fieldName]: defaultValue
}))
```

### Validation Dependencies

#### Cross-Field Validation
- **Operators**: Home and partner operators must be different
- **Date Range**: Valid from must be before valid to
- **Bounds**: Lower bound must be less than upper bound (when both present)
- **Service Types**: Must have at least one service type selected

#### Model-Specific Validation
- **All You Can Eat**: Only financial bounds allowed
- **IMSI Models**: Unique IMSI count bounds required
- **Balanced Models**: Balancing field becomes required
- **Tiered Models**: Multiple parameters with different bounds required

### Real-Time Field Updates

#### Dependent Field Updates
```javascript
// When home operators change, traffic segments are filtered
useEffect(() => {
  if (homeOperators.length === 0) {
    setTrafficSegments([]);
  }
}, [homeOperators]);

// When model type changes, reset dependent fields
useEffect(() => {
  resetParametersToDefault();
  updateAdditionalFieldsAvailability();
}, [modelType]);
```

#### Form Synchronization
- **Parameter quantity** adjusts based on model type limits
- **Field availability** updates immediately on model type change
- **Validation errors** clear when fields become unavailable
- **Default values** populate when fields become available

### Field Visibility and Conditional Logic

#### Implementation Pattern
The system uses a consistent pattern for conditional field rendering based on model type selection:

```javascript
// Example from AdditionalAttributes component
const isInboundMarketShareAvailable = (modelType) => {
  return modelType !== 'SEND_OR_PAY_FINANCIAL';
};

const isFinancialThresholdRequired = (modelType) => {
  return modelType === 'PER_MONTH_PER_IMSI_ABOVE_THRESHOLD';
};

const isBalancingRequired = (modelType) => {
  return [
    'BALANCED_UNBALANCED_SINGLE_RATE_EFFECTIVE',
    'BALANCED_UNBALANCED_STEPPED_TIERED'
  ].includes(modelType);
};

// Conditional rendering in component
{isInboundMarketShareAvailable(values.modelType) && (
  <InboundMarketShareField />
)}

{isFinancialThresholdRequired(values.modelType) && (
  <FinancialThresholdField required />
)}

{isBalancingRequired(values.modelType) && (
  <BalancingField required />
)}
```

#### Sub-Discount Specific Logic
Sub-discounts have additional conditional fields and restrictions:

```javascript
// Additional fields for sub-discounts only
const isSubDiscountContext = (context) => {
  return context === 'SUB_DISCOUNT';
};

// Rate above commitment field
{isSubDiscountContext(context) &&
 isRateAboveCommitmentAvailable(modelType) && (
  <RateAboveCommitmentField />
)}

// Above threshold rate field
{isSubDiscountContext(context) &&
 modelType === 'PER_MONTH_PER_IMSI_ABOVE_THRESHOLD' && (
  <AboveThresholdRateField />
)}
```

### Validation Implementation

#### Dynamic Validation Schema
```javascript
const getValidationSchema = (values, context) => {
  let schema = Yup.object().shape({
    modelType: Yup.string().required('Model type is required'),
    serviceTypes: Yup.array().min(1, 'At least one service type is required'),
  });

  // Add conditional validations based on model type
  if (isInboundMarketShareAvailable(values.modelType)) {
    schema = schema.concat(Yup.object().shape({
      isInboundMarketShare: Yup.boolean().required('Inbound market share selection is required'),
    }));
  }

  if (isFinancialThresholdRequired(values.modelType)) {
    schema = schema.concat(Yup.object().shape({
      financialThreshold: Yup.number()
        .required('Financial threshold is required')
        .min(0, 'Financial threshold must be positive'),
    }));
  }

  if (isBalancingRequired(values.modelType)) {
    schema = schema.concat(Yup.object().shape({
      balancing: Yup.string().required('Balancing selection is required'),
    }));
  }

  // Sub-discount specific validations
  if (context === 'SUB_DISCOUNT') {
    if (isRateAboveCommitmentAvailable(values.modelType)) {
      schema = schema.concat(Yup.object().shape({
        rateAboveCommitment: Yup.number()
          .min(0, 'Rate above commitment must be positive'),
      }));
    }
  }

  return schema;
};
```

#### Cross-Field Validation
```javascript
const validateCrossFields = (values) => {
  const errors = {};

  // Parameter bounds validation
  values.parameters?.forEach((param, index) => {
    if (param.lowerBound && param.upperBound &&
        param.lowerBound >= param.upperBound) {
      errors[`parameters[${index}].upperBound`] =
        'Upper bound must be greater than lower bound';
    }
  });

  // Commitment distribution validation
  if (values.commitmentDistribution) {
    const total = values.commitmentDistribution.reduce(
      (sum, item) => sum + (item.percentage || 0), 0
    );
    if (Math.abs(total - 100) > 0.01) {
      errors.commitmentDistribution = 'Distribution percentages must sum to 100%';
    }
  }

  // Service type compatibility validation
  if (isAccessFeeSpecific(values.modelType)) {
    const hasValidServiceTypes = values.serviceTypes?.some(
      serviceType => isAccessFeeServiceType(serviceType)
    );
    if (!hasValidServiceTypes) {
      errors.serviceTypes = 'IMSI-based models require access fee service types';
    }
  }

  return errors;
};
```

## Development Patterns & Best Practices

### Component Organization Principles

#### 1. Separation of Concerns
- **Container Components**: Handle data fetching and state management
- **Presentation Components**: Focus on UI rendering and user interaction
- **Logic Components**: Contain business logic and data transformation
- **Shared Components**: Reusable UI components across the application

#### 2. Context Usage Guidelines
- **Use Context for**: Entity-specific data that needs to be shared across multiple components
- **Avoid Context for**: Frequently changing data that would cause excessive re-renders
- **Context Optimization**: Use `useMemo` for context values to prevent unnecessary re-renders

#### 3. State Management Strategy
- **Redux for**: Global application state, API data, and cross-component communication
- **Local State for**: Component-specific UI state and temporary data
- **Context for**: Entity-specific data sharing within component trees

#### 4. Form Management Best Practices
- **Formik Integration**: Use Formik for complex forms with validation
- **Dynamic Schemas**: Implement dynamic validation based on form state
- **Error Handling**: Provide clear, actionable error messages
- **Performance**: Use `enableReinitialize` carefully to avoid unnecessary re-renders

### API Integration Patterns

#### 1. Service Layer Architecture
```javascript
// Service layer for API calls
export const discountService = {
  getDiscounts: (signal, agreementId, filters) =>
    HTTPService.get(`/budget-agreements/${agreementId}/discounts`, {
      params: filters,
      signal,
      paramsSerializer: params => qs.stringify(params, { arrayFormat: 'repeat' })
    }),

  createDiscount: (agreementId, data) =>
    HTTPService.post(`/budget-agreements/${agreementId}/discounts`, data),

  updateDiscount: (agreementId, discountId, data) =>
    HTTPService.patch(`/budget-agreements/${agreementId}/discounts/${discountId}`, data),

  deleteDiscount: (agreementId, discountId) =>
    HTTPService.delete(`/budget-agreements/${agreementId}/discounts/${discountId}`),
};
```

#### 2. Redux Action Patterns
```javascript
// Async action creator pattern
export const createDiscountAction = (agreementId, discountData) => async (dispatch) => {
  try {
    dispatch(createDiscountRequest());

    const transformedData = transformFormToAPI(discountData);
    const { data } = await discountService.createDiscount(agreementId, transformedData);

    dispatch(createDiscountSuccess(data));

    // Refresh discount list after creation
    dispatch(getDiscountsAction(agreementId));

    return data;
  } catch (error) {
    const errorInfo = handleAPIError(error);
    dispatch(createDiscountFailure(errorInfo));
    throw error;
  }
};
```

#### 3. Error Handling Strategy
```javascript
// Centralized error handling
const handleAPIError = (error) => {
  if (error.response) {
    const { status, data } = error.response;

    // Log error for debugging
    console.error('API Error:', { status, data, url: error.config?.url });

    // Return structured error information
    return {
      type: getErrorType(status),
      message: data.message || getDefaultErrorMessage(status),
      fields: data.errors || {},
      status,
    };
  }

  // Network or other errors
  return {
    type: 'NETWORK_ERROR',
    message: 'Network connection error. Please try again.',
  };
};
```

### Performance Optimization

#### 1. Component Optimization
```javascript
// Memoized components for expensive renders
const DiscountTable = React.memo(({ discounts, onEdit, onDelete }) => {
  return (
    <Table>
      {discounts.map(discount => (
        <DiscountRow
          key={discount.id}
          discount={discount}
          onEdit={onEdit}
          onDelete={onDelete}
        />
      ))}
    </Table>
  );
});

// Memoized context values
const BudgetDetailsContextProvider = ({ children }) => {
  const contextValue = useMemo(() => ({
    budgetId,
    globalBudgetFilters,
    setGlobalBudgetFilters,
    // ... other values
  }), [budgetId, globalBudgetFilters]);

  return (
    <BudgetDetailsContext.Provider value={contextValue}>
      {children}
    </BudgetDetailsContext.Provider>
  );
};
```

#### 2. Data Fetching Optimization
```javascript
// Request cancellation for cleanup
useEffect(() => {
  const abortController = new AbortController();

  dispatch(getDiscountsAction(agreementId, filters, abortController.signal));

  return () => {
    abortController.abort();
  };
}, [dispatch, agreementId, filters]);

// Debounced search for performance
const [searchTerm, setSearchTerm] = useState('');
const debouncedSearchTerm = useDebounce(searchTerm, 300);

useEffect(() => {
  if (debouncedSearchTerm) {
    dispatch(searchDiscountsAction(debouncedSearchTerm));
  }
}, [debouncedSearchTerm, dispatch]);
```

## Form Components Structure

### Component Hierarchy

#### Main Form Component
```
DiscountForm (Main Container)
├── DiscountModelType (Model Selection)
├── PerfectScrollbar (Scroll Container)
│   └── div.discount-form__accordions
│       ├── DiscountQualifying (Qualifying Rules)
│       ├── AdditionalAttributes (Optional Fields)
│       ├── DiscountTraffic (Core Traffic Fields)
│       ├── DiscountParameters (Calculation Parameters)
│       └── CommitmentDistribution (Financial Distribution)
└── DiscountFormErrorInfo (Error Display)
```

#### Modal Containers
```
DiscountCreation
├── DiscountCreationModalContent
│   ├── DiscountChilds (Sub-discount Management)
│   └── DiscountForm

SubDiscountCreation
├── SubDiscountCreationModalContent
│   └── DiscountForm (with isSubDiscount=true)

DiscountEditing
├── DiscountEditingModalContent
│   └── DiscountForm (with initial values)

SubDiscountEditing
├── SubDiscountEditingModalContent
│   └── DiscountForm (with isSubDiscount=true, initial values)
```

### Core Form Components

#### 1. DiscountForm (Main Container)
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountForm.jsx`

**Props**:
```javascript
{
  initialDiscountFormValues: Object,           // Pre-populated form data
  closeModal: Function,                        // Modal close handler
  submitForm: Function,                        // Form submission handler
  initialAdditionalFormFieldsConfiguration: Object, // Additional fields config
  isSubDiscount: Boolean                       // Sub-discount context flag
}
```

**Key Features**:
- Formik integration with Yup validation
- Model type state management
- Dynamic form configuration based on model type
- Additional fields configuration management
- Form submission with data transformation

**State Management**:
```javascript
const [modelType, setModelType] = useState(initialModelType);
const [additionalFormFieldsConfiguration, setAdditionalFormFieldsConfiguration] = useState(initialConfig);
```

#### 2. DiscountModelType (Model Selection)
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountModelType/DiscountModelType.jsx`

**Props**:
```javascript
{
  modelType: Object,        // Current model type selection
  setModelType: Function,   // Model type change handler
  isSubDiscount: Boolean    // Determines available model types
}
```

**Features**:
- Model type dropdown with filtered options
- Information tooltip about model type changes
- Different model types for main discounts vs sub-discounts
- Reset warning when changing model types

#### 3. DiscountTraffic (Core Fields)
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountTraffic/DiscountTraffic.jsx`

**Components**:
- **Operators Selection**: Home and partner operators
- **Date Range**: Validity period picker
- **Direction**: Traffic direction selection
- **Service Types**: Multi-select with model-specific options
- **Settlement & Currency**: Settlement method and currency selection
- **Tax & Volume Types**: Radio button selections

**Conditional Fields**:
- **Call Destinations**: Autocomplete with mutual exclusion
- **Called Countries**: Multi-select with mutual exclusion
- **IMSI Count Type**: For IMSI-based models
- **Traffic Segments**: Dependent on home operators

#### 4. DiscountParameters (Calculation Setup)
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/DiscountParameters.jsx`

**Features**:
- Dynamic parameter rows based on model type
- Add/remove parameter controls
- Model-specific field configurations
- Parameter validation and formatting

**Sub-Components**:
- **DiscountParameter**: Individual parameter row
- **DiscountParametersControls**: Add/remove buttons
- **DiscountParametersHead**: Column headers

**Fields per Parameter**:
- Calculation Type (dropdown)
- Discount Basis (dropdown, conditional)
- Basis Value (number input, conditional)
- Bound Type (dropdown)
- Lower/Upper Bounds (number inputs, conditional)
- Balancing (dropdown, conditional)
- Access Fee Rate (number input, conditional)
- Incremental Rate (number input, conditional)

#### 5. DiscountQualifying (Qualifying Rules)
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountQualifying/DiscountQualifying.jsx`

**Features**:
- Accordion-style collapsible section
- Status indicator (Empty/Partially Filled/Filled)
- Optional qualifying criteria setup

**Fields**:
- Direction (dropdown)
- Service Types (multi-select)
- Basis (dropdown)
- Lower/Upper Bounds (number inputs)

#### 6. AdditionalAttributes (Optional Fields)
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/AdditionalAttributes/AdditionalAttributes.jsx`

**Features**:
- Checkbox-based field activation
- Dynamic field rendering based on selection
- Context-aware field availability
- Field reset functionality when disabled

**Available Attributes**:
- Call Destinations
- Traffic Segments
- Rate Above Commitment (sub-discounts only)
- Inbound Market Share (main discounts only)
- Financial Threshold (main discounts only)
- Above Threshold Rate (sub-discounts only)

#### 7. CommitmentDistribution (Financial Distribution)
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountParameters/CommitmentDistribution/`

**Features**:
- Only available for specific model types
- Minimum 2 distribution parameters required
- Add/remove distribution controls
- Operator and charge configuration per distribution

**Sub-Components**:
- **CommitmentDistributionParameters**: Main container
- **CommitmentDistributionParameter**: Individual distribution row
- **CommitmentDistributionParametersControls**: Add/remove controls
- **CommitmentDistributionParametersHead**: Table headers

### Shared Components

#### 1. DiscountChilds (Sub-Discount Management)
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountChilds/DiscountChilds.jsx`

**Purpose**: Manage sub-discounts for parent discounts
**Features**:
- Sub-discount creation modal integration
- Sub-discount editing capabilities
- Sub-discount deletion
- Parent-child relationship management

#### 2. DiscountModalTitle
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountModalTitle/DiscountModalTitle.jsx`

**Purpose**: Standardized modal titles for discount operations
**Variants**: Creation, Editing, Sub-discount creation, Sub-discount editing

#### 3. DiscountFormErrorInfo
**Path**: `src/pages/AgreementDetails/AgreementItems/Discounts/shared/DiscountForm/DiscountFormErrorInfo/DiscountFormErrorInfo.jsx`

**Purpose**: Display form validation errors
**Features**: Formik error integration, user-friendly error messages

### Utility Components

#### 1. Autocomplete Components
- **OperatorsAutocomplete**: Operator selection with search
- **CurrenciesAutocomplete**: Currency selection with search
- **TrafficSegmentsAutocomplete**: Traffic segment selection
- **CalledCountriesSelect**: Country selection component

#### 2. Input Components
- **FormattedNumber**: Number input with formatting
- **RangePicker**: Date range selection
- **RadioButtons**: Radio button groups
- **Autocomplete**: Generic autocomplete component

### Form State Management

#### Formik Configuration
```javascript
const formik = useFormik({
  initialValues: initialDiscountFormValues,
  validationSchema: DiscountFormSchema,
  onSubmit: async (values) => {
    await submitForm(values);
  },
  enableReinitialize: true
});
```

#### State Synchronization
```javascript
// Model type changes trigger form reconfiguration
useEffect(() => {
  if (modelType?.value !== formik.values[discountModelTypeField]?.value) {
    // Reset parameters to default for new model type
    const defaultParameters = getDefaultParametersForModelType(modelType);
    formik.setFieldValue('parameters', defaultParameters);

    // Update additional fields availability
    setAdditionalFormFieldsConfiguration(getUpdatedConfig(modelType));
  }
}, [modelType]);
```

#### Dynamic Validation
```javascript
// Validation schema adapts to current form state
const DiscountFormSchema = Yup.object().shape({
  // Static validations
  [discountFields.homeOperators]: Yup.array().min(1),

  // Conditional validations based on model type
  [discountFields.inboundMarketShare]: Yup.number()
    .nullable(true)
    .when(discountModelTypeField, {
      is: (modelType) => supportsInboundMarketShare(modelType),
      then: Yup.number().test(boundsValidationConfig)
    }),

  // Parameter validations
  parameters: Yup.array().of(
    Yup.object().shape({
      [discountParametersFields.calculationType]: Yup.object().required(),
      // ... other parameter validations
    })
  )
});
```

### Component Communication Patterns

#### Props Drilling Pattern
```javascript
// Parent passes configuration down to children
<DiscountForm
  isSubDiscount={isSubDiscount}
  initialAdditionalFormFieldsConfiguration={config}
>
  <AdditionalAttributes
    isSubDiscount={isSubDiscount}
    isInboundMarketShare={isInboundMarketShare}
    isFinancialThreshold={isFinancialThreshold}
  />
</DiscountForm>
```

#### Callback Pattern
```javascript
// Child components communicate changes back to parent
<DiscountParameters
  formik={formik}
  discountFormConfig={discountFormConfig}
  onParameterChange={(newParameters) => {
    formik.setFieldValue('parameters', newParameters);
  }}
/>
```

#### Context Pattern
```javascript
// Agreement context provides shared data
const { agreementId } = useAgreementDetailsContext();
```

### Reusable Utilities

#### 1. Form Data Transformation
```javascript
// Convert form data to API format
export const getConvertedForRequestFormData = (formData) => { /* ... */ };

// Convert API data to form format
export const useInitialDiscountFormValues = (apiData) => { /* ... */ };
```

#### 2. Validation Utilities
```javascript
// Number format validation
export const ratesValidationConfig = {
  message: 'up to 6 digits integer part; up to 10 digits decimal part.',
  test: (value) => validateNumberFormat(value, 6, 10)
};

export const boundsValidationConfig = {
  message: 'up to 15 digits integer part; up to 5 digits decimal part.',
  test: (value) => validateNumberFormat(value, 15, 5)
};
```

#### 3. Configuration Utilities
```javascript
// Get model-specific configuration
export const useDiscountFormConfigByModelType = (modelType) => {
  return discountFormConfigByModelType[modelType?.value] || {};
};

// Get additional attributes configuration
export const getDefaultAdditionalAttributesConfig = (options) => { /* ... */ };
```

#### 4. Data Processing Utilities
```javascript
// Extract IDs from object arrays
export const getIds = (array) => array.map(item => item.id);

// Extract values by key from object arrays
export const getArrayByKey = (array, key) => array.map(item => item[key]);

// Convert and validate numbers
export const getConvertedFormattedNumber = (value) =>
  value === '' || value === null ? value : Number(value);
```

### Component Testing Patterns

#### Mock Data Structure
```javascript
const mockFormData = {
  [discountModelTypeField]: { value: 'SINGLE_RATE_EFFECTIVE' },
  [discountFields.homeOperators]: [{ id: 1, pmn_code: 'TEST' }],
  [discountFields.partnerOperators]: [{ id: 2, pmn_code: 'PARTNER' }],
  // ... other fields
};
```

#### Component Testing
```javascript
describe('DiscountForm', () => {
  const defaultProps = {
    closeModal: jest.fn(),
    submitForm: jest.fn(),
    initialDiscountFormValues: mockFormData,
    initialAdditionalFormFieldsConfiguration: mockConfig
  };

  test('renders form with initial values', () => {
    render(<DiscountForm {...defaultProps} />);
    // Test form rendering
  });

  test('handles model type changes', () => {
    // Test model type change behavior
  });
});
```

### Performance Considerations

#### Memoization
```javascript
// Memoize expensive calculations
const discountFormConfig = useMemo(() =>
  getDiscountFormConfigByModelType(modelType), [modelType]
);

// Memoize callback functions
const handleParameterChange = useCallback((newParameters) => {
  formik.setFieldValue('parameters', newParameters);
}, [formik]);
```

#### Lazy Loading
```javascript
// Lazy load heavy components
const CommitmentDistribution = lazy(() =>
  import('./CommitmentDistribution/CommitmentDistribution')
);
```

#### Conditional Rendering Optimization
```javascript
// Only render components when needed
{discountFormConfig.additionalFields?.[commitmentDistributionParameters] && (
  <CommitmentDistribution formik={formik} />
)}
```

## Complete Usage Examples

### End-to-End Workflow Example

#### Scenario: Creating a Complete Budget with Agreements and Discounts

##### Step 1: Create Budget
```javascript
// Budget creation data
const budgetData = {
  name: "Q1 2025 Telecommunications Budget",
  description: "First quarter budget for European operations",
  homeOperators: [36488, 36489, 36490], // Operator IDs
  type: "QUARTERLY",
  startDate: "2025-01-01",
  endDate: "2025-03-31",
  lastHistoricalMonth: "2024-12-01",
  creationMethod: "FILL_WITH_HISTORICAL_TRAFFIC"
};

// Create budget via Redux action
const result = await dispatch(createBudgetAction(budgetData));
const budgetId = result.id;

// Navigate to budget details
navigate(`/budgets/${budgetId}`);
```

##### Step 2: Create Agreement within Budget
```javascript
// Agreement creation data
const agreementData = {
  name: "Bilateral Agreement - Operator XYZ",
  description: "Voice and SMS traffic agreement",
  partnerOperators: [36491, 36492],
  agreementType: "BILATERAL",
  startDate: "2025-01-01",
  endDate: "2025-03-31",
  negotiatorId: 123,
  referenceNumber: "AGR-2025-Q1-001"
};

// Create agreement via Redux action
const agreementResult = await dispatch(
  createAgreementAction(budgetId, agreementData)
);
const agreementId = agreementResult.id;

// Navigate to agreement details
navigate(`/budgets/${budgetId}/agreements/${agreementId}`);
```

##### Step 3: Create Discount within Agreement
```javascript
// Discount creation data - Single Rate Effective model
const discountData = {
  modelType: "SINGLE_RATE_EFFECTIVE",
  serviceTypes: ["VOICE_CALL", "SMS"],
  discountBasis: "VALUE",
  settlementMethod: "CREDIT_NOTE_EOA",
  isInboundMarketShare: true,
  parameters: [{
    rate: 0.15, // 15% discount rate
    volumeLowerBound: 0,
    volumeUpperBound: 1000000,
    calculationType: "SINGLE_RATE_EFFECTIVE",
    boundType: "VOLUME"
  }],
  qualifyingRules: [
    {
      type: "MINIMUM_VOLUME",
      value: 50000,
      period: "MONTHLY"
    }
  ]
};

// Create discount via Redux action
const discountResult = await dispatch(
  createDiscountAction(agreementId, discountData)
);
```

##### Step 4: Create Sub-Discount
```javascript
// Sub-discount creation data
const subDiscountData = {
  modelType: "PER_MONTH_PER_IMSI_ABOVE_THRESHOLD",
  serviceTypes: ["DATA_ACCESS"],
  discountBasis: "VALUE",
  settlementMethod: "CREDIT_NOTE_EOA",
  financialThreshold: 10000,
  aboveThresholdRate: 0.20, // 20% rate above threshold
  parameters: [{
    rate: 0.10, // 10% base rate
    accessFeeRate: 5.00, // $5 access fee per IMSI
    imsiLowerBound: 1,
    calculationType: "PER_MONTH_PER_IMSI_ABOVE_THRESHOLD",
    boundType: "UNIQUE_IMSI_COUNT_PER_MONTH"
  }]
};

// Create sub-discount via Redux action
const subDiscountResult = await dispatch(
  createSubDiscountAction(agreementId, discountResult.id, subDiscountData)
);
```

### Complex Discount Configuration Examples

#### Example 1: Stepped/Tiered Discount
```javascript
const steppedDiscountData = {
  modelType: "STEPPED_TIERED",
  serviceTypes: ["VOICE_CALL"],
  discountBasis: "VOLUME",
  settlementMethod: "CREDIT_NOTE_EOM",
  isInboundMarketShare: false,
  parameters: [
    {
      rate: 0.05, // 5% for first tier
      volumeLowerBound: 0,
      volumeUpperBound: 100000,
      calculationType: "STEPPED_TIERED",
      boundType: "VOLUME"
    },
    {
      rate: 0.10, // 10% for second tier
      volumeLowerBound: 100001,
      volumeUpperBound: 500000,
      calculationType: "STEPPED_TIERED",
      boundType: "VOLUME"
    },
    {
      rate: 0.15, // 15% for third tier
      volumeLowerBound: 500001,
      volumeUpperBound: null, // No upper limit
      calculationType: "STEPPED_TIERED",
      boundType: "VOLUME"
    }
  ],
  qualifyingRules: [
    {
      type: "MINIMUM_MONTHLY_VOLUME",
      value: 10000,
      period: "MONTHLY"
    }
  ]
};
```

#### Example 2: Send or Pay Financial Model
```javascript
const sendOrPayFinancialData = {
  modelType: "SEND_OR_PAY_FINANCIAL",
  serviceTypes: ["VOICE_CALL", "SMS", "DATA"],
  settlementMethod: "CREDIT_NOTE_EOA",
  // Note: No discountBasis for financial models
  parameters: [{
    financialLowerBound: 50000, // $50,000 commitment
    calculationType: "SEND_OR_PAY_FINANCIAL",
    boundType: "FINANCIAL"
  }],
  commitmentDistribution: [
    {
      operatorPairId: "36488-36491",
      percentage: 60,
      minimumCommitment: 30000
    },
    {
      operatorPairId: "36489-36491",
      percentage: 40,
      minimumCommitment: 20000
    }
  ]
};
```

#### Example 3: Balanced/Unbalanced Model
```javascript
const balancedUnbalancedData = {
  modelType: "BALANCED_UNBALANCED_STEPPED_TIERED",
  serviceTypes: ["VOICE_CALL"],
  discountBasis: "VALUE",
  settlementMethod: "NETTING",
  isInboundMarketShare: true,
  balancing: "BALANCED", // Required for balanced/unbalanced models
  parameters: [
    {
      rate: 0.08, // 8% for balanced traffic
      volumeLowerBound: 0,
      volumeUpperBound: 200000,
      calculationType: "BALANCED_UNBALANCED_STEPPED_TIERED",
      boundType: "VOLUME"
    },
    {
      rate: 0.12, // 12% for higher volume balanced traffic
      volumeLowerBound: 200001,
      volumeUpperBound: null,
      calculationType: "BALANCED_UNBALANCED_STEPPED_TIERED",
      boundType: "VOLUME"
    }
  ]
};
```

### Form Validation Examples

#### Dynamic Validation Based on Model Type
```javascript
const DiscountForm = () => {
  const validationSchema = useMemo(() => {
    return Yup.object().shape({
      modelType: Yup.string().required('Model type is required'),

      serviceTypes: Yup.array()
        .min(1, 'At least one service type is required')
        .test('service-type-compatibility', 'Invalid service types for selected model',
          function(value) {
            const { modelType } = this.parent;
            if (modelType?.includes('PER_MONTH_PER_IMSI')) {
              return value?.some(type => isAccessFeeServiceType(type));
            }
            return true;
          }
        ),

      discountBasis: Yup.string().when('modelType', {
        is: (modelType) => !['SEND_OR_PAY_FINANCIAL', 'ALL_YOU_CAN_EAT'].includes(modelType),
        then: Yup.string().required('Discount basis is required'),
        otherwise: Yup.string().notRequired()
      }),

      isInboundMarketShare: Yup.boolean().when('modelType', {
        is: (modelType) => modelType !== 'SEND_OR_PAY_FINANCIAL',
        then: Yup.boolean().required('Inbound market share selection is required'),
        otherwise: Yup.boolean().notRequired()
      }),

      financialThreshold: Yup.number().when('modelType', {
        is: 'PER_MONTH_PER_IMSI_ABOVE_THRESHOLD',
        then: Yup.number()
          .required('Financial threshold is required')
          .min(0, 'Financial threshold must be positive'),
        otherwise: Yup.number().notRequired()
      }),

      balancing: Yup.string().when('modelType', {
        is: (modelType) => [
          'BALANCED_UNBALANCED_SINGLE_RATE_EFFECTIVE',
          'BALANCED_UNBALANCED_STEPPED_TIERED'
        ].includes(modelType),
        then: Yup.string().required('Balancing selection is required'),
        otherwise: Yup.string().notRequired()
      }),

      parameters: Yup.array().of(
        Yup.object().shape({
          rate: Yup.number()
            .required('Rate is required')
            .min(0, 'Rate must be positive')
            .max(1, 'Rate cannot exceed 100%'),

          volumeLowerBound: Yup.number()
            .min(0, 'Lower bound must be non-negative')
            .test('bounds-order', 'Lower bound must be less than upper bound',
              function(value) {
                const { volumeUpperBound } = this.parent;
                if (value != null && volumeUpperBound != null) {
                  return value < volumeUpperBound;
                }
                return true;
              }
            ),

          volumeUpperBound: Yup.number()
            .min(0, 'Upper bound must be non-negative')
        })
      ),

      commitmentDistribution: Yup.array().when('modelType', {
        is: 'SEND_OR_PAY_FINANCIAL',
        then: Yup.array()
          .min(2, 'At least 2 distribution entries required')
          .test('percentage-sum', 'Distribution percentages must sum to 100%',
            function(value) {
              if (!value) return false;
              const total = value.reduce((sum, item) => sum + (item.percentage || 0), 0);
              return Math.abs(total - 100) < 0.01;
            }
          ),
        otherwise: Yup.array().notRequired()
      })
    });
  }, []);

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
    >
      {/* Form content */}
    </Formik>
  );
};
```

### Error Handling Examples

#### API Error Handling with User Feedback
```javascript
const handleDiscountCreation = async (formData) => {
  try {
    setIsSubmitting(true);

    const result = await dispatch(createDiscountAction(agreementId, formData));

    // Success notification
    dispatch(showSuccessToast('Discount created successfully'));

    // Close modal and refresh data
    onClose();
    dispatch(getDiscountsAction(agreementId));

  } catch (error) {
    const errorInfo = handleAPIError(error);

    switch (errorInfo.type) {
      case 'VALIDATION_ERROR':
        // Show field-specific errors
        setFieldErrors(errorInfo.fields);
        dispatch(showErrorToast('Please correct the highlighted errors'));
        break;

      case 'AUTHENTICATION_ERROR':
        dispatch(showErrorToast('Session expired. Please log in again'));
        redirectToLogin();
        break;

      case 'AUTHORIZATION_ERROR':
        dispatch(showErrorToast('You do not have permission to perform this action'));
        break;

      case 'NETWORK_ERROR':
        dispatch(showErrorToast('Network error. Please check your connection and try again'));
        break;

      default:
        dispatch(showErrorToast(errorInfo.message || 'An unexpected error occurred'));
    }
  } finally {
    setIsSubmitting(false);
  }
};
```

### Testing Examples

#### Component Testing with React Testing Library
```javascript
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import DiscountCreationModal from './DiscountCreationModal';
import { createMockStore } from '../../../test-utils';

describe('DiscountCreationModal', () => {
  let mockStore;

  beforeEach(() => {
    mockStore = createMockStore({
      discounts: { data: [], isLoading: false, error: null },
      createDiscount: { isLoading: false, error: null }
    });
  });

  const renderComponent = (props = {}) => {
    return render(
      <Provider store={mockStore}>
        <BrowserRouter>
          <DiscountCreationModal
            isOpen={true}
            onClose={jest.fn()}
            agreementId="123"
            {...props}
          />
        </BrowserRouter>
      </Provider>
    );
  };

  test('renders discount creation form', () => {
    renderComponent();

    expect(screen.getByText('Create Discount')).toBeInTheDocument();
    expect(screen.getByLabelText('Model Type')).toBeInTheDocument();
    expect(screen.getByLabelText('Service Types')).toBeInTheDocument();
  });

  test('shows conditional fields based on model type selection', async () => {
    renderComponent();

    // Select model type that requires financial threshold
    const modelTypeSelect = screen.getByLabelText('Model Type');
    fireEvent.change(modelTypeSelect, {
      target: { value: 'PER_MONTH_PER_IMSI_ABOVE_THRESHOLD' }
    });

    await waitFor(() => {
      expect(screen.getByLabelText('Financial Threshold')).toBeInTheDocument();
    });
  });

  test('validates required fields on submit', async () => {
    renderComponent();

    const submitButton = screen.getByText('Create');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Model type is required')).toBeInTheDocument();
      expect(screen.getByText('At least one service type is required')).toBeInTheDocument();
    });
  });

  test('calls create discount action on valid form submission', async () => {
    const mockCreateDiscount = jest.fn().mockResolvedValue({ id: '456' });
    mockStore.dispatch = mockCreateDiscount;

    renderComponent();

    // Fill out form
    fireEvent.change(screen.getByLabelText('Model Type'), {
      target: { value: 'SINGLE_RATE_EFFECTIVE' }
    });

    // Submit form
    fireEvent.click(screen.getByText('Create'));

    await waitFor(() => {
      expect(mockCreateDiscount).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'CREATE_DISCOUNT_REQUEST'
        })
      );
    });
  });
});
```

This comprehensive guide provides a complete understanding of the Budget Agreement Discount System, covering everything from the initial budget creation through complex discount configurations, including all architectural details, component structures, validation patterns, and practical usage examples.

### 1. Creating a Simple Single Rate Effective Discount

#### Step-by-Step Process
1. **Select Model Type**: Choose "Single Rate Effective (SRE)"
2. **Configure Traffic**:
   - Home Operators: Select at least one operator
   - Partner Operators: Select at least one operator
   - Validity Period: Set start and end dates
   - Direction: Choose Inbound/Outbound/Both
   - Service Types: Select Voice, SMS, Data, etc.
   - Settlement Method: Credit Note EoA (auto-selected)
   - Currency: Select currency
   - Tax Type: NET or GROSS
   - Volume Type: ACTUAL or BILLED

3. **Configure Parameters**:
   - Calculation Type: Single Rate Effective (auto-selected)
   - Discount Basis: Value
   - Basis Value: Enter discount rate (e.g., 0.05)
   - Bound Type: Volume
   - Lower Bound: Enter minimum volume (e.g., 1000)
   - Upper Bound: Optional maximum volume

4. **Optional Qualifying Rules**:
   - Direction: Optional traffic direction filter
   - Service Types: Optional service type filter
   - Basis: Volume/Market Share/IMSI Count/Average Usage
   - Lower Bound: Minimum threshold for qualification
   - Upper Bound: Optional maximum threshold

#### Example Configuration
```json
{
  "model_type": "SINGLE_RATE_EFFECTIVE",
  "home_operators": [{"id": 1, "pmn_code": "UKRAS"}],
  "partner_operators": [{"id": 2, "pmn_code": "USATT"}],
  "start_date": "2025-01-01",
  "end_date": "2025-12-31",
  "direction": "INBOUND",
  "service_types": ["VOICE", "SMS"],
  "settlement_method": "CREDIT_NOTE_EOA",
  "currency_code": "USD",
  "tax_type": "NET",
  "volume_type": "ACTUAL",
  "parameters": [{
    "calculation_type": "SINGLE_RATE_EFFECTIVE",
    "basis": "VALUE",
    "basis_value": 0.05,
    "bound_type": "VOLUME",
    "lower_bound": 1000,
    "upper_bound": null
  }]
}
```

### 2. Creating a Complex Send or Pay Financial Discount

#### Configuration Steps
1. **Model Type**: "Send or Pay Financial"
2. **Traffic Configuration**: Standard traffic fields
3. **Parameters**:
   - Calculation Type: Send or Pay Financial (auto-selected)
   - Bound Type: Financial Commitment (auto-selected)
   - Lower Bound: Minimum financial commitment
   - Basis and Basis Value: Disabled for this model

4. **Commitment Distribution** (Required):
   - Add minimum 2 distribution parameters
   - Each parameter needs home operators, partner operators, and charge percentage
   - Total charges should typically sum to 100%

#### Example Configuration
```json
{
  "model_type": "SEND_OR_PAY_FINANCIAL",
  "parameters": [{
    "calculation_type": "SEND_OR_PAY_FINANCIAL",
    "bound_type": "FINANCIAL_COMMITMENT",
    "lower_bound": 100000
  }],
  "commitment_distribution_parameters": [
    {
      "home_operators": [1],
      "partner_operators": [2],
      "charge": 60
    },
    {
      "home_operators": [3],
      "partner_operators": [4],
      "charge": 40
    }
  ]
}
```

### 3. Creating a Per Month Per IMSI Above Threshold Discount

#### Configuration Steps
1. **Model Type**: "Per Month Per IMSI - Above Threshold"
2. **Service Types**: Only Access Fee service types available
3. **Parameters**:
   - Calculation Type: Per Month Per IMSI - Above Threshold
   - Discount Basis: Value
   - Basis Value: Rate per IMSI
   - Bound Type: Unique IMSI Count per Month
   - Lower Bound: Minimum IMSI count threshold
   - Access Fee Rate: Monthly access fee per IMSI

4. **Additional Attributes**:
   - Financial Threshold: Available for this model type
   - Above Threshold Rate: Available for sub-discounts

#### Example Configuration
```json
{
  "model_type": "PER_MONTH_PER_IMSI_ABOVE_THRESHOLD",
  "service_types": ["ACCESS_FEE"],
  "parameters": [{
    "calculation_type": "PER_MONTH_PER_IMSI_ABOVE_THRESHOLD",
    "basis": "VALUE",
    "basis_value": 2.50,
    "bound_type": "UNIQUE_IMSI_COUNT_PER_MONTH",
    "lower_bound": 1000,
    "access_fee_rate": 5.00
  }],
  "financial_threshold": 50000
}
```

### 4. Creating Sub-Discounts

#### Process
1. **Create Parent Discount**: Use any main discount model type
2. **Add Sub-Discount**: Click "Add Sub-Discount" in parent discount
3. **Limited Model Types**: Only specific model types available for sub-discounts
4. **Different Fields**: Rate above commitment and above threshold rate available
5. **Restrictions**: No inbound market share, financial threshold, or commitment distribution

#### Sub-Discount Example
```json
{
  "model_type": "SINGLE_RATE_EFFECTIVE",
  "parameters": [{
    "calculation_type": "SINGLE_RATE_EFFECTIVE",
    "basis": "VALUE",
    "basis_value": 0.03,
    "bound_type": "VOLUME",
    "lower_bound": 5000
  }],
  "above_commitment_rate": 0.02
}
```

### 5. Using Additional Attributes

#### Call Destinations vs Called Countries
```javascript
// Mutually exclusive - can only use one
if (callDestinations.length > 0) {
  calledCountries = []; // Disabled
}
if (calledCountries.length > 0) {
  callDestinations = []; // Disabled
}
```

#### Traffic Segments
```javascript
// Requires home operators to be selected first
if (homeOperators.length === 0) {
  trafficSegments = []; // Disabled
}
```

### 6. Validation Examples

#### Rate Validation
```javascript
// Valid rates: up to 6 integer digits, 10 decimal digits
const validRates = [
  0.05,           // ✅ Valid
  123456.1234567890, // ✅ Valid
  1234567.0,      // ❌ Too many integer digits
  0.12345678901   // ❌ Too many decimal digits
];
```

#### Bounds Validation
```javascript
// Valid bounds: up to 15 integer digits, 5 decimal digits
const validBounds = [
  1000,                    // ✅ Valid
  123456789012345.12345,   // ✅ Valid
  1234567890123456.0,      // ❌ Too many integer digits
  1000.123456              // ❌ Too many decimal digits
];
```

### 7. Error Handling Examples

#### Form Validation Errors
```javascript
// Required field errors
{
  home_operators: "At least 1 home operator is required",
  service_types: "At least 1 service type is required",
  parameters: {
    0: {
      calculation_type: "Calculation type is required"
    }
  }
}
```

#### API Error Responses
```javascript
// Server validation error
{
  "detail": "Invalid operator combination for selected service types"
}

// Multiple validation errors
[
  "Home operators cannot be the same as partner operators",
  "Financial threshold must be greater than lower bound"
]
```

### 8. Common Configuration Patterns

#### Balanced Traffic Model
```json
{
  "model_type": "BALANCED_UNBALANCED_SINGLE_RATE_EFFECTIVE",
  "parameters": [{
    "calculation_type": "SINGLE_RATE_EFFECTIVE",
    "balancing": "BALANCED",
    "basis": "VALUE",
    "basis_value": 0.04
  }]
}
```

#### Tiered Discount Model
```json
{
  "model_type": "STEPPED_TIERED",
  "parameters": [
    {
      "calculation_type": "STEPPED_TIERED",
      "basis": "VALUE",
      "basis_value": 0.05,
      "bound_type": "VOLUME",
      "lower_bound": 0,
      "upper_bound": 10000
    },
    {
      "calculation_type": "STEPPED_TIERED",
      "basis": "VALUE",
      "basis_value": 0.03,
      "bound_type": "VOLUME",
      "lower_bound": 10000,
      "upper_bound": 50000
    }
  ]
}
```

### 9. Best Practices

#### Model Type Selection
- **Single Rate Effective**: Simple, uniform discount rates
- **Stepped/Tiered**: Volume-based progressive discounts
- **Send or Pay**: Traffic direction flexibility
- **IMSI-based**: Subscriber count-based pricing
- **All You Can Eat**: Unlimited usage scenarios

#### Parameter Configuration
- **Always validate bounds**: Lower bound should be less than upper bound
- **Consider business logic**: Ensure discount rates make business sense
- **Test edge cases**: Verify behavior at boundary values
- **Document assumptions**: Clear business rules for each model type

#### Additional Attributes Usage
- **Call Destinations**: Use for specific destination-based discounts
- **Traffic Segments**: Use for granular traffic classification
- **Qualifying Rules**: Use to add eligibility criteria
- **Financial Thresholds**: Use for commitment-based models

#### Sub-Discount Strategy
- **Hierarchical Structure**: Use sub-discounts for exceptions to main rules
- **Rate Inheritance**: Sub-discounts can override parent rates
- **Simplified Models**: Sub-discounts have limited model type options
- **Clear Relationships**: Maintain clear parent-child relationships

### 10. Troubleshooting Common Issues

#### Form Validation Issues
```javascript
// Issue: Form not submitting
// Solution: Check all required fields are filled
const requiredFields = [
  'home_operators', 'partner_operators', 'start_date', 'end_date',
  'direction', 'service_types', 'settlement_method', 'currency_code',
  'tax_type', 'volume_type'
];

// Issue: Parameter validation failing
// Solution: Ensure calculation_type matches model_type requirements
if (modelType === 'SINGLE_RATE_EFFECTIVE') {
  calculationType = 'SINGLE_RATE_EFFECTIVE'; // Must match
}
```

#### Model Type Configuration Issues
```javascript
// Issue: Fields not appearing/disappearing correctly
// Solution: Check model type configuration
const config = discountFormConfigByModelType[modelType.value];
if (!config.availableValues[fieldName]) {
  // Field not available for this model type
}

// Issue: Service types not loading
// Solution: Check service type configuration function
const serviceTypeConfig = config.availableValues.service_types;
const availableServiceTypes = serviceTypeConfig.getAvailableConfig();
```

#### API Integration Issues
```javascript
// Issue: Data transformation errors
// Solution: Verify data format before API call
const transformedData = getConvertedForRequestFormData(formData);
console.log('Transformed data:', transformedData);

// Issue: Validation errors from API
// Solution: Check API response format
catch (error) {
  const errorMessage = error.response.data?.detail ||
                      error.response.data?.[0] ||
                      'Unknown error occurred';
}
```

This comprehensive documentation covers all aspects of the Budget Agreement Discount System, providing developers and users with detailed information about functionality, configuration options, validation rules, API endpoints, and practical usage examples.

---

# Frontend Implementation Deep Dive

## Component Implementation Details

### 1. DiscountForm Component (Main Container)

#### State Management
```javascript
const DiscountForm = ({
  initialAdditionalFormFieldsConfiguration,
  initialDiscountFormValues,
  closeModal,
  submitForm,
  isSubDiscount,
}) => {
  // Form state management
  const [initialFormikValues, setInitialFormikValues] = useState(initialDiscountFormValues);

  // Parameter control triggers
  const [triggerAddParameterBtn, setTriggerAddParameterBtn] = useState(0);
  const [triggerDeleteParameterBtn, setTriggerDeleteParameterBtn] = useState(0);

  // Commitment distribution triggers
  const [triggerAddCommitmentDistributionParameterBtn, setTriggerAddCommitmentDistributionParameterBtn] = useState(0);
  const [triggerDeleteCommitmentDistributionParameterBtn, setTriggerDeleteCommitmentDistributionParameterBtn] = useState({ trigger: 0, index: undefined });

  // Commitment split toggle
  const [commitmentSplit, setCommitmentSplit] = useState(
    !!initialDiscountFormValues?.[commitmentDistributionParameters]
  );

  // Model type state
  const [modelType, setModelType] = useState(
    initialDiscountFormValues?.[discountModelTypeField] || modelTypesOptions.singleRateEffective
  );
```

#### Custom Hooks Integration
```javascript
// Get model-specific configuration
const {
  discountFormConfig,
  defaultDiscountFormConfigByModelType,
} = useDiscountFormConfigByModelType({
  modelType,
  initialFormikValues,
  triggerAddParameterBtn,
  triggerDeleteParameterBtn,
  triggerAddCommitmentDistributionParameterBtn,
  triggerDeleteCommitmentDistributionParameterBtn,
});

// Get default form values based on model type
const {
  defaultDiscountFormValuesByModelType,
} = useDefaultDiscountFormValuesByModelType({
  modelType,
  initialFormikValues,
  discountFormConfig: defaultDiscountFormConfigByModelType,
});

// Additional attributes configuration
const [additionalFormFieldsConfiguration, setAdditionalFormFieldsConfiguration] = useState(
  initialAdditionalFormFieldsConfiguration
);
```

#### Form Lifecycle Management
```javascript
// Reset initial values when model type changes
const resetInitialFormikValues = () => {
  setInitialFormikValues(null);
};

// Effect: Reset initial values when model type changes
useEffect(() => {
  if (modelType !== initialDiscountFormValues?.[discountModelTypeField]) {
    resetInitialFormikValues();
  }
}, [modelType]);

// Effect: Update form values when default values change
useEffect(() => {
  formik.setValues(defaultDiscountFormValuesByModelType);
}, [defaultDiscountFormValuesByModelType]);
```

#### Formik Configuration
```javascript
const formik = useFormik({
  initialValues: defaultDiscountFormValuesByModelType,
  validationSchema: DiscountFormSchema,
  validateOnChange: false,  // Validate only on submit
  validateOnBlur: false,    // Validate only on submit
  onSubmit: (values) => submitForm({
    ...values,
    [discountModelTypeField]: modelType,
  }),
});
```

#### Conditional Rendering Logic
```javascript
// Pass model-specific configuration to child components
<AdditionalAttributes
  additionalFormFieldsConfiguration={additionalFormFieldsConfiguration}
  setAdditionalFormFieldsConfiguration={setAdditionalFormFieldsConfiguration}
  formik={formik}
  isSubDiscount={isSubDiscount}
  isInboundMarketShare={
    !!discountFormConfig.availableValues?.[discountFields.inboundMarketShare]
  }
  isFinancialThreshold={
    !!discountFormConfig.availableValues?.[discountFields.financialThreshold]
  }
/>
```

### 2. DiscountModelType Component

#### Model Type Selection Logic
```javascript
const DiscountModelType = ({
  modelType,
  setModelType,
  isSubDiscount,
}) => {
  // Filter available options based on context
  const options = isSubDiscount ? availableSubDiscountModelTypes : availableDiscountModelTypes;

  const changeDiscountModelType = (newVal) => {
    setModelType(newVal);
  };

  return (
    <div className={`${discountModelTypeModifier}__wrap`}>
      <Autocomplete
        data-testid={discountModelTypeModifier}
        className={discountModelTypeModifier}
        options={options}
        getOptionLabel={(option) => (option.title ? option.title : '')}
        isOptionEqualToValue={(option, value) => option.title === value.title}
        value={modelType}
        onChange={(e, value) => changeDiscountModelType(value)}
        disableClearable
        renderInput={(params) => (
          <TextField
            {...params}
            variant="outlined"
            placeholder="Search"
            label="Model Type"
          />
        )}
      />
      <InfoIconWithTooltip
        infoText={infoText}
        tooltipPlacement="right"
      />
    </div>
  );
};
```

#### Model Type Constants
```javascript
// Available model types for main discounts
export const availableDiscountModelTypes = [
  modelTypesOptions.singleRateEffective,
  modelTypesOptions.steppedTiered,
  modelTypesOptions.sendOrPayTrafficSRE,
  // ... all 14 model types
];

// Limited model types for sub-discounts
export const availableSubDiscountModelTypes = [
  modelTypesOptions.singleRateEffective,
  modelTypesOptions.steppedTiered,
  modelTypesOptions.backToFirst,
  // ... 8 allowed sub-discount types
];

// Warning message for model type changes
export const infoText = (
  <>
    Changing the Model Type will <b>reset</b> the <b>Discount Model</b>
    <br />
    <b>Parameters</b> setup to the state available for the selected model.
    <br />
    Please review and update it as needed after the change.
  </>
);
```

### 3. AdditionalAttributes Component

#### Configuration Management
```javascript
const AdditionalAttributes = ({
  additionalFormFieldsConfiguration,
  setAdditionalFormFieldsConfiguration,
  formik,
  isSubDiscount,
  isInboundMarketShare,
  isFinancialThreshold,
}) => {
  // Get default configuration based on context
  const defaultAdditionalAttributesConfig = getDefaultAdditionalAttributesConfig({
    formik,
    isSubDiscount,
    isInboundMarketShare,
    isFinancialThreshold,
  });

  // Local state for attribute configuration
  const [additionalAttributesConfig, setAdditionalAttributesConfig] = useState(
    defaultAdditionalAttributesConfig
  );

  // Count active attributes for display
  const activeAdditionalAttributeQuantity = Object.values(additionalAttributesConfig)
    .filter(({ value }) => value).length;
```

#### Checkbox Change Handler
```javascript
const changeAttribute = (e) => {
  const { name, checked } = e.target;

  // Update local configuration
  setAdditionalAttributesConfig((prevState) => ({
    ...prevState,
    [name]: {
      ...prevState[name],
      value: checked,
    },
  }));

  // Update parent configuration
  setAdditionalFormFieldsConfiguration((prevState) => ({
    ...prevState,
    ...additionalAttributesConfig[name].fields.reduce((acc, field) => ({
      ...acc,
      [field]: checked,
    }), {}),
  }));

  // Reset field values when unchecked
  if (!checked) {
    additionalAttributesConfig[name].resetFieldsValues();
  }
};
```

#### Configuration Synchronization
```javascript
// Sync with parent configuration changes
useEffect(() => {
  const newAdditionalAttributesConfig = { ...additionalAttributesConfig };

  Object.entries(additionalFormFieldsConfiguration).forEach(([field, isActive]) => {
    Object.entries(newAdditionalAttributesConfig).forEach(([key, config]) => {
      if (config.fields.includes(field)) {
        newAdditionalAttributesConfig[key] = {
          ...config,
          value: isActive,
        };
      }
    });
  });

  setAdditionalAttributesConfig(newAdditionalAttributesConfig);
}, [additionalFormFieldsConfiguration]);

// Update configuration when context changes
useEffect(() => {
  const newConfig = getDefaultAdditionalAttributesConfig({
    formik,
    isSubDiscount,
    isInboundMarketShare,
    isFinancialThreshold,
  });
  setAdditionalAttributesConfig(newConfig);
}, [isSubDiscount, isInboundMarketShare, isFinancialThreshold]);
```

#### Conditional Configuration Logic
```javascript
export const getDefaultAdditionalAttributesConfig = ({
  formik,
  isSubDiscount,
  isInboundMarketShare,
  isFinancialThreshold,
}) => {
  // Base configuration (always available)
  const defaultAdditionalAttributesConfigForDiscount = {
    callDestinations: {
      value: false,
      title: 'Call Destinations',
      fields: [discountFields.callDestinations, discountFields.calledCountries],
      resetFieldsValues: () => formik.setValues((values) => ({
        ...values,
        [discountFields.callDestinations]: [],
        [discountFields.calledCountries]: [],
      })),
    },
    trafficSegments: {
      value: false,
      title: 'Traffic Segments',
      fields: [discountFields.trafficSegments],
      resetFieldsValues: () => formik.setValues((values) => ({
        ...values,
        [discountFields.trafficSegments]: [],
      })),
    },
  };

  // Sub-discount specific configurations
  const rateAboveCommitmentConfig = {
    rateAboveCommitment: {
      value: false,
      title: 'Rate Above Commitment',
      fields: [discountFields.rateAboveCommitment],
      resetFieldsValues: () => formik.setValues((values) => ({
        ...values,
        [discountFields.rateAboveCommitment]: null,
      })),
    },
  };

  const aboveThresholdRateConfig = {
    aboveThresholdRate: {
      value: false,
      title: 'Above Threshold Rate',
      fields: [discountFields.aboveThresholdRate],
      resetFieldsValues: () => formik.setValues((values) => ({
        ...values,
        [discountFields.aboveThresholdRate]: null,
      })),
    },
  };

  // Main discount specific configurations
  const inboundMarketShareConfig = {
    inboundMarketShare: {
      value: false,
      title: 'Inbound Market Share',
      fields: [discountFields.inboundMarketShare],
      resetFieldsValues: () => formik.setValues((values) => ({
        ...values,
        [discountFields.inboundMarketShare]: null,
      })),
    },
  };

  const financialThresholdConfig = {
    financialThreshold: {
      value: false,
      title: 'Financial Threshold',
      fields: [discountFields.financialThreshold],
      resetFieldsValues: () => formik.setValues((values) => ({
        ...values,
        [discountFields.financialThreshold]: null,
      })),
    },
  };

  // Conditional configuration assembly
  return {
    ...defaultAdditionalAttributesConfigForDiscount,
    ...(isSubDiscount && rateAboveCommitmentConfig),
    ...(!isSubDiscount && isInboundMarketShare && inboundMarketShareConfig),
    ...(!isSubDiscount && isFinancialThreshold && financialThresholdConfig),
    ...(isSubDiscount && aboveThresholdRateConfig),
  };
};
```

#### Accordion Rendering
```javascript
return (
  <div className={additionalAttributesModifier}>
    <Accordion>
      <AccordionSummary
        expandIcon={<GrDown fontSize="20" />}
        className={additionalAttributesTitleWrapModifier}
      >
        <Typography className={additionalAttributesTitleModifier}>
          Additional attributes
          <ComponentQuantity quantity={activeAdditionalAttributeQuantity} />
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        {Object.entries(additionalAttributesConfig).map(([key, value]) => (
          <FormControlLabel
            label={value.title}
            key={value.title}
            control={(
              <Checkbox
                checked={value.value}
                name={key}
                onChange={changeAttribute}
              />
            )}
          />
        ))}
      </AccordionDetails>
    </Accordion>
  </div>
);
```

### 4. DiscountTraffic Component

#### Field Change Handler
```javascript
const changeDiscountTraffic = ({ field, value }) => {
  formik.setValues((values) => ({
    ...values,
    [field]: value,
  }));
};

// Error styling helper
const getErrorModifier = (field) => (formik.errors[field] ? errorModifier : '');
```

#### Conditional Field Rendering
```javascript
// Call Destinations - disabled when Called Countries selected
{additionalFormFieldsConfiguration[discountFields.callDestinations] && (
  <Autocomplete
    name={discountFields.callDestinations}
    className={`${discountTrafficAutocompleteModifier} ${getErrorModifier(discountFields.callDestinations)}`}
    options={callDestinations}
    value={formik.values[discountFields.callDestinations]}
    onChange={(e, value) => changeDiscountTraffic({
      field: discountFields.callDestinations,
      value,
    })}
    multiple
    disabled={!!formik.values[discountFields.calledCountries].length}
    renderInput={(params) => (
      <TextField
        {...params}
        variant="outlined"
        placeholder="Search"
        label="Call Destinations"
      />
    )}
  />
)}

// Called Countries - disabled when Call Destinations selected
{additionalFormFieldsConfiguration[discountFields.calledCountries] && (
  <CalledCountriesSelect
    changeDiscountTraffic={changeDiscountTraffic}
    formikValues={formik.values}
    disabled={!!formik.values[discountFields.callDestinations].length}
  />
)}
```

#### Service Types Configuration
```javascript
// Service types with model-specific configuration
<Autocomplete
  name={discountFields.serviceTypes}
  className={`${discountTrafficAutocompleteModifier} ${getErrorModifier(discountFields.serviceTypes)}`}
  options={discountFormConfig.availableValues[discountFields.serviceTypes]?.getAvailableConfig() || []}
  value={formik.values[discountFields.serviceTypes]}
  onChange={(e, value) => changeDiscountTraffic({
    field: discountFields.serviceTypes,
    value,
  })}
  multiple
  renderInput={(params) => (
    <TextField
      {...params}
      variant="outlined"
      placeholder="Search"
      label="Service Types*"
    />
  )}
  // Conditional select all option
  {...(discountFormConfig.availableValues[discountFields.serviceTypes]?.enableSelectAll && {
    selectAllOption: true,
    selectAllText: 'Select All'
  })}
/>
```

#### Traffic Segments with Dependencies
```javascript
// Traffic Segments - requires home operators
{additionalFormFieldsConfiguration[discountFields.trafficSegments] && (
  <TrafficSegmentsAutocomplete
    changeDiscountTraffic={changeDiscountTraffic}
    trafficSegmentsValue={formik.values[discountFields.trafficSegments]}
    homeOperators={formik.values[discountFields.homeOperators]}
    disabled={!formik.values[discountFields.homeOperators].length}
  />
)}
```

### 5. DiscountParameters Component

#### Parameter Management
```javascript
const DiscountParameters = ({
  formik,
  additionalFormFieldsConfiguration,
  discountFormConfig,
  setTriggerAddParameterBtn,
  setTriggerDeleteParameterBtn,
  // ... other props
}) => (
  <div className={discountParametersWrapModifier}>
    <DiscountSectionTitle title="Discount Model Parameters" />
    <PerfectScrollbar>
      <Table className={discountParametersModifier}>
        <DiscountParametersHead
          additionalFormFieldsConfiguration={additionalFormFieldsConfiguration}
          additionalFieldsFromDiscountFormConfig={discountFormConfig.additionalFields}
        />
        <TableBody>
          {formik.values.parameters?.map((item, index) => (
            <DiscountParameter
              formik={formik}
              index={index}
              key={`parameter-row-${index}`}
              discountParameterConfig={
                discountFormConfig.availableValues.parameters[index]
              }
              commitmentSplit={commitmentSplit}
            />
          ))}
        </TableBody>
      </Table>
    </PerfectScrollbar>

    {/* Parameter Controls */}
    <DiscountParametersControls
      formik={formik}
      minParametersQuantity={discountFormConfig.minParametersQuantity}
      maxParametersQuantity={discountFormConfig.maxParametersQuantity}
      setTriggerAddParameterBtn={setTriggerAddParameterBtn}
      setTriggerDeleteParameterBtn={setTriggerDeleteParameterBtn}
    />
  </div>
);
```

#### Individual Parameter Row
```javascript
const DiscountParameter = ({
  formik,
  index,
  discountParameterConfig,
  commitmentSplit,
}) => {
  const changeDiscountParameter = ({ field, value }) => {
    const newParameters = [...formik.values.parameters];
    newParameters[index] = {
      ...newParameters[index],
      [field]: value,
    };
    formik.setFieldValue('parameters', newParameters);
  };

  return (
    <TableRow>
      {/* Calculation Type */}
      <TableCell>
        <Autocomplete
          options={discountParameterConfig[discountParametersFields.calculationType] || []}
          value={formik.values.parameters[index][discountParametersFields.calculationType]}
          onChange={(e, value) => changeDiscountParameter({
            field: discountParametersFields.calculationType,
            value,
          })}
          disabled={discountParameterConfig[discountParametersFields.calculationType]?.length <= 1}
        />
      </TableCell>

      {/* Discount Basis */}
      <TableCell>
        <Autocomplete
          options={discountParameterConfig[discountParametersFields.discountBasis] || []}
          value={formik.values.parameters[index][discountParametersFields.discountBasis]}
          onChange={(e, value) => changeDiscountParameter({
            field: discountParametersFields.discountBasis,
            value,
          })}
          disabled={!discountParameterConfig[discountParametersFields.discountBasis]?.length}
        />
      </TableCell>

      {/* Basis Value */}
      <TableCell>
        <FormattedNumber
          value={formik.values.parameters[index][discountParametersFields.discountBasisValue]}
          onChange={(value) => changeDiscountParameter({
            field: discountParametersFields.discountBasisValue,
            value,
          })}
          disabled={discountParameterConfig[discountParametersFields.discountBasisValue]?.isDisabled}
          validationConfig={ratesValidationConfig}
        />
      </TableCell>

      {/* Additional conditional fields based on configuration */}
    </TableRow>
  );
};
```

### 6. DiscountQualifying Component

#### Qualifying State Management
```javascript
const DiscountQualifying = ({ formik }) => {
  // Custom hook for qualifying state
  const {
    discountQualifyingState,
  } = useDiscountQualifyingState(formik.values[discountQualifyingRule]);

  const getErrorModifier = (field) => (
    formik.errors[discountQualifyingRule]?.[field] ? errorModifier : ''
  );

  const changeDiscountQualifying = ({ field, value }) => {
    formik.setValues((values) => ({
      ...values,
      [discountQualifyingRule]: {
        ...values[discountQualifyingRule],
        [field]: value,
      },
    }));
  };
```

#### Qualifying State Hook
```javascript
const useDiscountQualifyingState = (qualifyingRule) => {
  const [discountQualifyingState, setDiscountQualifyingState] = useState(
    discountQualifyingStates.empty
  );

  useEffect(() => {
    const requiredFields = [
      discountQualifyingFields.direction,
      discountQualifyingFields.serviceTypes,
      discountQualifyingFields.basis,
      discountQualifyingFields.lowerBound,
    ];

    const filledRequiredFields = requiredFields.filter(field => {
      const value = qualifyingRule?.[field];
      return value && (Array.isArray(value) ? value.length > 0 : true);
    });

    if (filledRequiredFields.length === 0) {
      setDiscountQualifyingState(discountQualifyingStates.empty);
    } else if (filledRequiredFields.length === requiredFields.length) {
      setDiscountQualifyingState(discountQualifyingStates.filled);
    } else {
      setDiscountQualifyingState(discountQualifyingStates.partiallyFilled);
    }
  }, [qualifyingRule]);

  return { discountQualifyingState };
};
```

#### Status Display Component
```javascript
const DiscountQualifyingStatus = ({ discountQualifyingState }) => {
  switch (discountQualifyingState) {
    case discountQualifyingStates.filled:
      return (
        <span
          className="discount-qualifying-status"
          style={{ background: getBrandColors(styles.greenColor500)[50] }}
        >
          <AiOutlineCheckCircle
            size={18}
            color={styles.greenColor500}
          />
        </span>
      );
    case discountQualifyingStates.partiallyFilled:
      return (
        <AlertIconWithTooltip
          alertText="Please fill all required fields or clear all fields"
          size={18}
        />
      );
    default:
      return null;
  }
};
```

### 7. Custom Hooks Implementation

#### useDiscountFormConfigByModelType Hook
```javascript
const useDiscountFormConfigByModelType = ({
  modelType,
  initialFormikValues,
  triggerAddParameterBtn,
  triggerDeleteParameterBtn,
  triggerAddCommitmentDistributionParameterBtn,
  triggerDeleteCommitmentDistributionParameterBtn,
}) => {
  // Get base configuration for model type
  const defaultDiscountFormConfigByModelType = discountFormConfigByModelType[
    modelType.value
  ];

  // Adjust configuration based on initial values
  const getConfigBasedOnInitialValues = () => {
    const lastParameterIndex = defaultDiscountFormConfigByModelType
      .availableValues.parameters.length - 1;
    const lastParameter = defaultDiscountFormConfigByModelType
      .availableValues.parameters[lastParameterIndex];
    const diffParametersAmount = initialFormikValues.parameters.length
        - defaultDiscountFormConfigByModelType.availableValues.parameters.length;

    if (diffParametersAmount < 0) {
      return defaultDiscountFormConfigByModelType;
    }

    // Add additional parameter configurations
    const newParameters = Array(diffParametersAmount).fill(undefined).map(() => lastParameter);
    const configBasedOnInitialValues = cloneDeep(defaultDiscountFormConfigByModelType);
    configBasedOnInitialValues.availableValues.parameters.push(...newParameters);

    return configBasedOnInitialValues;
  };

  // Initialize configuration
  const initialConfig = initialFormikValues
    ? getConfigBasedOnInitialValues()
    : defaultDiscountFormConfigByModelType;

  const [discountFormConfig, setDiscountFormConfig] = useState(initialConfig);

  // Add parameter configuration
  const addParameterToConfig = () => {
    const lastParameterIndex = discountFormConfig.availableValues.parameters.length - 1;
    const lastParameter = discountFormConfig.availableValues.parameters[lastParameterIndex];

    setDiscountFormConfig((prevState) => ({
      ...prevState,
      availableValues: {
        ...prevState.availableValues,
        parameters: [...prevState.availableValues.parameters, lastParameter],
      },
    }));
  };

  // Remove parameter configuration
  const deleteParameterFromConfig = () => {
    setDiscountFormConfig((prevState) => ({
      ...prevState,
      availableValues: {
        ...prevState.availableValues,
        parameters: prevState.availableValues.parameters.slice(0, -1),
      },
    }));
  };

  // Effects for parameter management
  useEffect(() => {
    if (triggerAddParameterBtn) {
      addParameterToConfig();
    }
  }, [triggerAddParameterBtn]);

  useEffect(() => {
    if (triggerDeleteParameterBtn) {
      deleteParameterFromConfig();
    }
  }, [triggerDeleteParameterBtn]);

  // Effect for model type changes
  useEffect(() => {
    const newConfig = initialFormikValues
    && modelType === initialFormikValues[discountModelTypeField]
      ? getConfigBasedOnInitialValues()
      : defaultDiscountFormConfigByModelType;

    setDiscountFormConfig(newConfig);
  }, [modelType]);

  return {
    defaultDiscountFormConfigByModelType,
    discountFormConfig,
  };
};
```

#### useDefaultDiscountFormValuesByModelType Hook
```javascript
const useDefaultDiscountFormValuesByModelType = ({
  modelType,
  initialFormikValues,
  discountFormConfig,
}) => {
  const [defaultDiscountFormValuesByModelType, setDefaultDiscountFormValuesByModelType] = useState({});

  // Generate default values based on model type
  const generateDefaultValues = () => {
    if (initialFormikValues && modelType === initialFormikValues[discountModelTypeField]) {
      return initialFormikValues;
    }

    // Base default values
    const baseDefaults = {
      [discountFields.homeOperators]: [],
      [discountFields.partnerOperators]: [],
      [discountFields.validFrom]: null,
      [discountFields.validTo]: '',
      [discountFields.discountDirection]: null,
      [discountFields.serviceTypes]: [],
      [discountFields.discountSettlementMethod]: discountFormConfig.availableValues[discountFields.discountSettlementMethod]?.[0] || null,
      [discountFields.discountCurrency]: null,
      [discountFields.taxType]: taxTypeOptions.net,
      [discountFields.volumeType]: volumeTypeOptions.actual,
      // Additional attributes
      [discountFields.callDestinations]: [],
      [discountFields.calledCountries]: [],
      [discountFields.trafficSegments]: [],
      [discountFields.rateAboveCommitment]: null,
      [discountFields.inboundMarketShare]: null,
      [discountFields.financialThreshold]: null,
      [discountFields.aboveThresholdRate]: null,
      // Parameters
      parameters: generateDefaultParameters(),
      // Qualifying rule
      [discountQualifyingRule]: {
        [discountQualifyingFields.direction]: null,
        [discountQualifyingFields.serviceTypes]: [],
        [discountQualifyingFields.basis]: null,
        [discountQualifyingFields.lowerBound]: null,
        [discountQualifyingFields.upperBound]: null,
      },
      // Commitment distribution
      [commitmentDistributionParameters]: null,
    };

    return baseDefaults;
  };

  // Generate default parameters based on configuration
  const generateDefaultParameters = () => {
    return discountFormConfig.availableValues.parameters.map((paramConfig) => ({
      [discountParametersFields.calculationType]: paramConfig[discountParametersFields.calculationType]?.[0] || null,
      [discountParametersFields.discountBasis]: paramConfig[discountParametersFields.discountBasis]?.[0] || null,
      [discountParametersFields.discountBasisValue]: null,
      [discountParametersFields.boundType]: paramConfig[discountParametersFields.boundType]?.[0] || null,
      [discountParametersFields.lowerBound]: null,
      [discountParametersFields.upperBound]: null,
      [discountParametersFields.balancing]: paramConfig[discountParametersFields.balancing]?.[0] || null,
      [discountParametersFields.accessFeeRate]: null,
      [discountParametersFields.incrementalRate]: null,
    }));
  };

  // Update default values when dependencies change
  useEffect(() => {
    const newDefaults = generateDefaultValues();
    setDefaultDiscountFormValuesByModelType(newDefaults);
  }, [modelType, discountFormConfig, initialFormikValues]);

  return {
    defaultDiscountFormValuesByModelType,
  };
};
```

### 8. Form Validation Schema Implementation

#### Dynamic Validation Schema
```javascript
const DiscountFormSchema = Yup.object().shape({
  // Required fields
  [discountFields.homeOperators]: Yup.array()
    .min(1, 'At least 1 home operator is required')
    .required('Home operators are required'),

  [discountFields.partnerOperators]: Yup.array()
    .min(1, 'At least 1 partner operator is required')
    .required('Partner operators are required'),

  [discountFields.validFrom]: Yup.date()
    .required('Valid from date is required')
    .nullable(),

  [discountFields.validTo]: Yup.string()
    .required('Valid to date is required'),

  [discountFields.discountDirection]: Yup.object()
    .required('Direction is required')
    .nullable(),

  [discountFields.serviceTypes]: Yup.array()
    .min(1, 'At least 1 service type is required')
    .required('Service types are required'),

  // Conditional validations for additional attributes
  [discountFields.rateAboveCommitment]: Yup.number()
    .nullable(true)
    .test(ratesValidationConfig),

  [discountFields.inboundMarketShare]: Yup.number()
    .nullable(true)
    .test(boundsValidationConfig),

  [discountFields.financialThreshold]: Yup.number()
    .nullable(true)
    .test(boundsValidationConfig),

  [discountFields.aboveThresholdRate]: Yup.number()
    .nullable(true)
    .test(ratesValidationConfig),

  // Parameters validation
  parameters: Yup.array().of(
    Yup.object().shape({
      [discountParametersFields.calculationType]: Yup.object()
        .required('Calculation type is required')
        .nullable(),

      [discountParametersFields.discountBasis]: Yup.object()
        .nullable(),

      [discountParametersFields.discountBasisValue]: Yup.number()
        .nullable(true)
        .test(ratesValidationConfig),

      [discountParametersFields.boundType]: Yup.object()
        .nullable(),

      [discountParametersFields.lowerBound]: Yup.number()
        .nullable(true)
        .test(boundsValidationConfig),

      [discountParametersFields.upperBound]: Yup.number()
        .nullable(true)
        .test(boundsValidationConfig)
        .test('upper-bound-greater', 'Upper bound must be greater than lower bound', function(value) {
          const { lowerBound } = this.parent;
          if (value && lowerBound) {
            return value > lowerBound;
          }
          return true;
        }),
    })
  ).min(1, 'At least 1 parameter is required'),

  // Qualifying rule validation
  [discountQualifyingRule]: Yup.object().shape({
    [discountQualifyingFields.direction]: Yup.object().nullable(),
    [discountQualifyingFields.serviceTypes]: Yup.array(),
    [discountQualifyingFields.basis]: Yup.object().nullable(),
    [discountQualifyingFields.lowerBound]: Yup.number()
      .nullable(true)
      .test(boundsValidationConfig),
    [discountQualifyingFields.upperBound]: Yup.number()
      .nullable(true)
      .test(boundsValidationConfig),
  }).test('qualifying-rule-consistency', 'Complete all required qualifying fields or clear all', function(value) {
    const requiredFields = [
      discountQualifyingFields.direction,
      discountQualifyingFields.serviceTypes,
      discountQualifyingFields.basis,
      discountQualifyingFields.lowerBound,
    ];

    const filledFields = requiredFields.filter(field => {
      const fieldValue = value?.[field];
      return fieldValue && (Array.isArray(fieldValue) ? fieldValue.length > 0 : true);
    });

    // Either all required fields filled or none
    return filledFields.length === 0 || filledFields.length === requiredFields.length;
  }),

  // Commitment distribution validation
  [commitmentDistributionParameters]: Yup.array().of(
    Yup.object().shape({
      [commitmentDistributionParametersFields.homeOperators]: Yup.array()
        .min(1, 'At least 1 home operator is required'),
      [commitmentDistributionParametersFields.partnerOperators]: Yup.array()
        .min(1, 'At least 1 partner operator is required'),
      [commitmentDistributionParametersFields.charge]: Yup.number()
        .required('Charge is required')
        .test(boundsValidationConfig),
    })
  ).nullable().test('min-commitment-params', 'At least 2 commitment distribution parameters are required', function(value) {
    if (value === null) return true;
    return value.length >= 2;
  }),
});
```

#### Validation Configuration Objects
```javascript
// Rate validation: 6 integer digits, 10 decimal digits
export const ratesValidationConfig = {
  name: 'rate-format',
  message: 'up to 6 digits integer part; up to 10 digits decimal part.',
  test: (value) => {
    if (value === null || value === undefined || value === '') return true;

    const stringValue = value.toString();
    const [integerPart, decimalPart = ''] = stringValue.split('.');

    return integerPart.length <= 6 && decimalPart.length <= 10;
  }
};

// Bounds validation: 15 integer digits, 5 decimal digits
export const boundsValidationConfig = {
  name: 'bounds-format',
  message: 'up to 15 digits integer part; up to 5 digits decimal part.',
  test: (value) => {
    if (value === null || value === undefined || value === '') return true;

    const stringValue = value.toString();
    const [integerPart, decimalPart = ''] = stringValue.split('.');

    return integerPart.length <= 15 && decimalPart.length <= 5;
  }
};
```

### 9. Error Handling and Display

#### DiscountFormErrorInfo Component
```javascript
const DiscountFormErrorInfo = ({ errors }) => {
  const [errorFields, setErrorFields] = useState([]);

  // Extract error fields from Formik errors
  useEffect(() => {
    const extractErrorFields = (errorsObj, prefix = '') => {
      const fields = [];

      Object.entries(errorsObj).forEach(([key, value]) => {
        const fieldPath = prefix ? `${prefix}.${key}` : key;

        if (typeof value === 'string') {
          fields.push({
            field: fieldPath,
            message: value,
          });
        } else if (typeof value === 'object' && value !== null) {
          if (Array.isArray(value)) {
            value.forEach((item, index) => {
              if (typeof item === 'object') {
                fields.push(...extractErrorFields(item, `${fieldPath}[${index}]`));
              }
            });
          } else {
            fields.push(...extractErrorFields(value, fieldPath));
          }
        }
      });

      return fields;
    };

    const fields = extractErrorFields(errors);
    setErrorFields(fields);
  }, [errors]);

  if (errorFields.length === 0) return null;

  return (
    <div className="discount-form-error-info">
      <Typography variant="h6" color="error">
        Please fix the following errors:
      </Typography>
      <ul>
        {errorFields.map(({ field, message }, index) => (
          <li key={`${field}-${index}`}>
            <Typography variant="body2" color="error">
              <strong>{getFieldDisplayName(field)}:</strong> {message}
            </Typography>
          </li>
        ))}
      </ul>
    </div>
  );
};

// Helper function to get user-friendly field names
const getFieldDisplayName = (fieldPath) => {
  const fieldMappings = {
    [discountFields.homeOperators]: 'Home Operators',
    [discountFields.partnerOperators]: 'Partner Operators',
    [discountFields.validFrom]: 'Valid From',
    [discountFields.validTo]: 'Valid To',
    [discountFields.discountDirection]: 'Direction',
    [discountFields.serviceTypes]: 'Service Types',
    'parameters[0].calculation_type': 'Parameter 1 - Calculation Type',
    'parameters[0].lower_bound': 'Parameter 1 - Lower Bound',
    // ... more mappings
  };

  return fieldMappings[fieldPath] || fieldPath;
};
```

### 10. Component Communication Patterns

#### Props Drilling Pattern
```javascript
// Parent component passes configuration down
const DiscountForm = () => {
  return (
    <form onSubmit={formik.handleSubmit}>
      <DiscountModelType
        modelType={modelType}
        setModelType={setModelType}
        isSubDiscount={isSubDiscount}
      />

      <AdditionalAttributes
        additionalFormFieldsConfiguration={additionalFormFieldsConfiguration}
        setAdditionalFormFieldsConfiguration={setAdditionalFormFieldsConfiguration}
        formik={formik}
        isSubDiscount={isSubDiscount}
        isInboundMarketShare={!!discountFormConfig.availableValues?.[discountFields.inboundMarketShare]}
        isFinancialThreshold={!!discountFormConfig.availableValues?.[discountFields.financialThreshold]}
      />

      <DiscountTraffic
        formik={formik}
        additionalFormFieldsConfiguration={additionalFormFieldsConfiguration}
        discountFormConfig={discountFormConfig}
      />

      <DiscountParameters
        formik={formik}
        discountFormConfig={discountFormConfig}
        setTriggerAddParameterBtn={setTriggerAddParameterBtn}
        setTriggerDeleteParameterBtn={setTriggerDeleteParameterBtn}
      />
    </form>
  );
};
```

#### Callback Pattern for State Updates
```javascript
// Child component communicates changes back to parent
const DiscountParameters = ({
  setTriggerAddParameterBtn,
  setTriggerDeleteParameterBtn,
}) => {
  const addParameter = () => {
    setTriggerAddParameterBtn(prev => prev + 1);
  };

  const deleteParameter = () => {
    setTriggerDeleteParameterBtn(prev => prev + 1);
  };

  return (
    <DiscountParametersControls
      onAddParameter={addParameter}
      onDeleteParameter={deleteParameter}
    />
  );
};
```

#### Context Pattern for Shared Data
```javascript
// Agreement context provides shared data
const AgreementDetailsContext = createContext();

export const useAgreementDetailsContext = () => {
  const context = useContext(AgreementDetailsContext);
  if (!context) {
    throw new Error('useAgreementDetailsContext must be used within AgreementDetailsProvider');
  }
  return context;
};

// Usage in components
const DiscountForm = () => {
  const { agreementId, operators, currencies } = useAgreementDetailsContext();

  // Use shared data in form
};
```

### 11. Performance Optimization Patterns

#### Memoization
```javascript
// Memoize expensive calculations
const DiscountForm = () => {
  const discountFormConfig = useMemo(() =>
    getDiscountFormConfigByModelType(modelType),
    [modelType]
  );

  const defaultValues = useMemo(() =>
    generateDefaultValues(modelType, discountFormConfig),
    [modelType, discountFormConfig]
  );

  // Memoize callback functions
  const handleParameterChange = useCallback((index, field, value) => {
    const newParameters = [...formik.values.parameters];
    newParameters[index] = { ...newParameters[index], [field]: value };
    formik.setFieldValue('parameters', newParameters);
  }, [formik]);

  const handleModelTypeChange = useCallback((newModelType) => {
    setModelType(newModelType);
    // Reset form when model type changes
    formik.resetForm();
  }, [formik, setModelType]);
};
```

#### Conditional Rendering Optimization
```javascript
// Only render components when needed
const AdditionalAttributes = ({ isInboundMarketShare, isFinancialThreshold }) => {
  return (
    <div>
      {/* Always render base attributes */}
      <CallDestinationsField />
      <TrafficSegmentsField />

      {/* Conditionally render based on model type */}
      {isInboundMarketShare && <InboundMarketShareField />}
      {isFinancialThreshold && <FinancialThresholdField />}

      {/* Conditionally render based on context */}
      {isSubDiscount && (
        <>
          <RateAboveCommitmentField />
          <AboveThresholdRateField />
        </>
      )}
    </div>
  );
};
```

#### Lazy Loading
```javascript
// Lazy load heavy components
const CommitmentDistribution = lazy(() =>
  import('./CommitmentDistribution/CommitmentDistribution')
);

const DiscountParameters = () => {
  return (
    <div>
      {/* Regular parameters */}
      <ParametersTable />

      {/* Lazy load commitment distribution when needed */}
      <Suspense fallback={<div>Loading...</div>}>
        {showCommitmentDistribution && (
          <CommitmentDistribution />
        )}
      </Suspense>
    </div>
  );
};
```

### 12. Testing Patterns

#### Component Testing Setup
```javascript
describe('DiscountForm', () => {
  const mockProps = {
    initialDiscountFormValues: {
      [discountModelTypeField]: modelTypesOptions.singleRateEffective,
      [discountFields.homeOperators]: [],
      [discountFields.partnerOperators]: [],
      parameters: [{
        [discountParametersFields.calculationType]: null,
        [discountParametersFields.discountBasis]: null,
      }],
    },
    initialAdditionalFormFieldsConfiguration: {
      [discountFields.callDestinations]: false,
      [discountFields.trafficSegments]: false,
    },
    closeModal: jest.fn(),
    submitForm: jest.fn(),
    isSubDiscount: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders form with initial values', () => {
    render(<DiscountForm {...mockProps} />);

    expect(screen.getByTestId('discount-form')).toBeInTheDocument();
    expect(screen.getByLabelText('Model Type')).toBeInTheDocument();
  });

  test('handles model type changes', async () => {
    render(<DiscountForm {...mockProps} />);

    const modelTypeSelect = screen.getByLabelText('Model Type');
    fireEvent.click(modelTypeSelect);

    const steppedTieredOption = screen.getByText('Stepped/Tiered');
    fireEvent.click(steppedTieredOption);

    // Verify form resets with new model type configuration
    await waitFor(() => {
      expect(screen.getByDisplayValue('Stepped/Tiered')).toBeInTheDocument();
    });
  });

  test('validates required fields on submit', async () => {
    render(<DiscountForm {...mockProps} />);

    const submitButton = screen.getByText('Confirm');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('At least 1 home operator is required')).toBeInTheDocument();
      expect(screen.getByText('At least 1 partner operator is required')).toBeInTheDocument();
    });
  });
});
```

#### Mock Data Patterns
```javascript
// Mock discount data
export const mockDiscountData = {
  [discountModelTypeField]: modelTypesOptions.singleRateEffective,
  [discountFields.homeOperators]: [
    { id: 1, pmn_code: 'TEST_HOME' }
  ],
  [discountFields.partnerOperators]: [
    { id: 2, pmn_code: 'TEST_PARTNER' }
  ],
  [discountFields.validFrom]: '2025-01-01',
  [discountFields.validTo]: '2025-12-31',
  [discountFields.discountDirection]: directionOptions.inbound,
  [discountFields.serviceTypes]: [serviceTypeOptions.voice],
  parameters: [{
    [discountParametersFields.calculationType]: calculationTypesOptions.singleRateEffective,
    [discountParametersFields.discountBasis]: discountBasisOptions.value,
    [discountParametersFields.discountBasisValue]: 0.05,
    [discountParametersFields.boundType]: boundTypeOptions.volume,
    [discountParametersFields.lowerBound]: 1000,
    [discountParametersFields.upperBound]: null,
  }],
};

// Mock API responses
export const mockApiResponses = {
  createDiscount: {
    data: { id: 123, ...mockDiscountData }
  },
  getDiscounts: {
    data: [mockDiscountData]
  },
  editDiscount: {
    data: { id: 123, ...mockDiscountData }
  },
};
```

This comprehensive frontend implementation documentation provides detailed insights into how each component works, their state management patterns, conditional rendering logic, form validation, error handling, and testing approaches. It covers all the frontend logic and conditional rendering mechanisms used in the discount system.
